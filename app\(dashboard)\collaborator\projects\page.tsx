'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { CollaboratorProjectsMetrics } from '@/components/projects/collaborator-projects-metrics';
import { CollaboratorProjectsTable } from '@/components/tables/collaborator-projects/collaborator-projects-table';
import { useProjects, usePortfolio } from '@/hooks/use-db';
import type { Project } from '@/lib/supabase/database-modules';

export default function CollaboratorProjectsPage() {
  const { getCollaboratorProjects } = useProjects();
  const { portfolioData } = usePortfolio();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!portfolioData?.id) return;

    setLoading(true);

    getCollaboratorProjects(portfolioData.id)
      .then((collaboratorProjects) => {
        setProjects(collaboratorProjects);
      })
      .catch((error) => {
        console.error('Error fetching collaborator projects:', error);
        toast.error('Failed to load projects');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [portfolioData?.id, getCollaboratorProjects]);

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>My Projects</p>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <CollaboratorProjectsMetrics projects={projects} loading={loading} />
        <CollaboratorProjectsTable projects={projects} loading={loading} />
      </section>
    </main>
  );
}
