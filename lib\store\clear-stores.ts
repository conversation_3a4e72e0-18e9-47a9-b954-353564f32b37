'use client';

import { isEditingStore } from './is-editing';
import { userStore } from './user';
import { useSearchStore } from './search-store';
import { useViewStore } from './view-store';
import { useFilterStore } from './filter-store';
import { useCreateIssueStore } from './create-issue-store';

/**
 * Utility function to clear all Zustand stores when a user logs out
 * This ensures no state persists between user sessions
 */
export function clearAllStores() {
  // Clear user store
  userStore.getState().removeUser(null);
  userStore.getState().removeProfile(null);

  // Clear editing store
  isEditingStore.getState().setIsEditing(false);

  // Clear UI stores
  useSearchStore.getState().clearSearch();
  useViewStore.getState().setViewType('list');
  useFilterStore.getState().clearFilters();
  useCreateIssueStore.getState().closeDialog();

  console.log('All stores cleared successfully');
}
