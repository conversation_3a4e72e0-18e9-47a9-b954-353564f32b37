'use client';

import { useState } from 'react';
import { Check, Plus, X, Users } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { useMembers } from '@/hooks/use-db';
import type { Member } from '@/lib/supabase/database-modules';

interface SelectedMember {
  id: string;
  name: string;
  email: string;
  avatar_url: string | null;
  role: 'lead' | 'member' | 'viewer';
}

interface ProjectMembersSelectorProps {
  selectedMembers: SelectedMember[];
  onMembersChange: (members: SelectedMember[]) => void;
  disabled?: boolean;
}

export function ProjectMembersSelector({
  selectedMembers,
  onMembersChange,
  disabled = false,
}: ProjectMembersSelectorProps) {
  const { members } = useMembers();
  const [open, setOpen] = useState(false);

  const availableMembers = members.filter(
    (member) =>
      !selectedMembers.some((selected) => selected.id === member.id) &&
      member.role !== 'Affiliate' // Exclude affiliates from project membership
  );

  const addMember = (
    member: Member,
    role: 'lead' | 'member' | 'viewer' = 'member'
  ) => {
    // Check if the user is an affiliate
    if (member.role === 'Affiliate') {
      console.warn('Attempted to add affiliate as project member:', member);
      return;
    }

    const newMember: SelectedMember = {
      id: member.id,
      name: member.full_name || member.name || member.email,
      email: member.email,
      avatar_url: member.avatar_url,
      role,
    };
    onMembersChange([...selectedMembers, newMember]);
    setOpen(false);
  };

  const removeMember = (memberId: string) => {
    onMembersChange(selectedMembers.filter((member) => member.id !== memberId));
  };

  const updateMemberRole = (
    memberId: string,
    role: 'lead' | 'member' | 'viewer'
  ) => {
    onMembersChange(
      selectedMembers.map((member) =>
        member.id === memberId ? { ...member, role } : member
      )
    );
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'lead':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'member':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'viewer':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div className='space-y-3'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Users className='h-4 w-4 text-muted-foreground' />
          <span className='text-sm font-medium'>Project Members</span>
          {selectedMembers.length > 0 && (
            <Badge variant='secondary' className='text-xs'>
              {selectedMembers.length}
            </Badge>
          )}
        </div>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              size='sm'
              disabled={disabled}
              className='h-8'
            >
              <Plus className='h-4 w-4 mr-2' />
              Add Member
            </Button>
          </PopoverTrigger>
          <PopoverContent className='w-80 p-0' align='end'>
            <Command>
              <CommandInput placeholder='Search members...' />
              <CommandList>
                <CommandEmpty>No members found.</CommandEmpty>
                <CommandGroup>
                  {availableMembers.map((member) => (
                    <CommandItem
                      key={member.id}
                      onSelect={() => addMember(member)}
                      className='flex items-center gap-3 p-3'
                    >
                      <Avatar className='h-8 w-8'>
                        <AvatarImage src={member.avatar_url || ''} />
                        <AvatarFallback>
                          {(member.full_name || member.name || member.email)
                            .charAt(0)
                            .toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className='flex-1 min-w-0'>
                        <p className='text-sm font-medium truncate'>
                          {member.full_name || member.name || 'Unknown'}
                        </p>
                        <p className='text-xs text-muted-foreground truncate'>
                          {member.email}
                        </p>
                      </div>
                      <Check className='h-4 w-4 opacity-0' />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      {selectedMembers.length === 0 ? (
        <div className='text-center py-6 text-muted-foreground'>
          <Users className='h-8 w-8 mx-auto mb-2 opacity-50' />
          <p className='text-sm'>No members selected</p>
          <p className='text-xs'>
            Add team members to collaborate on this project
          </p>
        </div>
      ) : (
        <div className='space-y-2'>
          {selectedMembers.map((member) => (
            <div
              key={member.id}
              className='flex items-center justify-between p-3 border rounded-lg'
            >
              <div className='flex items-center gap-3'>
                <Avatar className='h-8 w-8'>
                  <AvatarImage src={member.avatar_url || ''} />
                  <AvatarFallback>
                    {member.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className='flex-1 min-w-0'>
                  <p className='text-sm font-medium truncate'>{member.name}</p>
                  <p className='text-xs text-muted-foreground truncate'>
                    {member.email}
                  </p>
                </div>
              </div>
              <div className='flex items-center gap-2'>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant='ghost'
                      size='sm'
                      className={`h-6 px-2 text-xs ${getRoleColor(member.role)}`}
                      disabled={disabled}
                    >
                      {member.role}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className='w-32 p-1' align='end'>
                    <div className='space-y-1'>
                      {(['lead', 'member', 'viewer'] as const).map((role) => (
                        <Button
                          key={role}
                          variant='ghost'
                          size='sm'
                          className='w-full justify-start h-8 text-xs'
                          onClick={() => updateMemberRole(member.id, role)}
                        >
                          {role === member.role && (
                            <Check className='h-3 w-3 mr-2' />
                          )}
                          {role === member.role ? '' : <span className='w-5' />}
                          {role}
                        </Button>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
                <Button
                  variant='ghost'
                  size='sm'
                  className='h-6 w-6 p-0 text-muted-foreground hover:text-destructive'
                  onClick={() => removeMember(member.id)}
                  disabled={disabled}
                >
                  <X className='h-3 w-3' />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
