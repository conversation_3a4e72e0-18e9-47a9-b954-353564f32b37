'use client';

import {
  Layers,
  Target,
  TrendingDown,
  TrendingUp,
  User<PERSON>heck,
  Users,
} from 'lucide-react';
import { useMemo } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type {
  Cycle,
  Issue,
  Team,
  TeamMember,
} from '@/lib/supabase/database-modules';

interface TeamsMetricsProps {
  teams: Team[];
  teamMembers: TeamMember[];
  issues?: Issue[];
  cycles?: Cycle[];
  loading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  trend?: 'up' | 'down' | 'neutral';
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  icon?: React.ReactNode;
}

function MetricCard({
  title,
  value,
  subtitle,
  trend,
  variant = 'default',
  icon,
}: MetricCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'destructive':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      default:
        return 'border-border bg-card';
    }
  };

  const getTrendIcon = () => {
    if (trend === 'up')
      return <TrendingUp className='h-4 w-4 text-green-600' />;
    if (trend === 'down')
      return <TrendingDown className='h-4 w-4 text-red-600' />;
    return null;
  };

  return (
    <Card className={`${getVariantStyles()} transition-all`}>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium text-muted-foreground'>
          {title}
        </CardTitle>
        {icon && <div className='text-muted-foreground'>{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className='flex items-center justify-between'>
          <div>
            <div className='text-2xl font-bold'>{value}</div>
            <div className='flex items-center gap-1 text-xs text-muted-foreground'>
              {getTrendIcon()}
              <span>{subtitle}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function TeamsMetrics({
  teams,
  teamMembers,
  issues = [],
  cycles = [],
  loading,
}: TeamsMetricsProps) {
  const metrics = useMemo(() => {
    if (!teams.length) {
      return {
        total: 0,
        activeTeams: 0,
        totalMembers: 0,
        averageMembersPerTeam: 0,
        newTeams: 0,
        recentlyJoinedMembers: 0,
      };
    }

    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const total = teams.length;

    // Count teams with active members
    const activeTeams = teams.filter((team) =>
      teamMembers.some(
        (member) => member.team_id === team.id && member.joined === true
      )
    ).length;

    // Count total active team members
    const totalMembers = teamMembers.filter(
      (member) => member.joined === true
    ).length;

    // Calculate average members per team
    const averageMembersPerTeam =
      total > 0 ? Math.round(totalMembers / total) : 0;

    // Count new teams created in the last week
    const newTeams = teams.filter((team) => {
      if (!team.created_at) return false;
      const createdDate = new Date(team.created_at);
      return createdDate >= lastWeek;
    }).length;

    // Count recently joined members
    const recentlyJoinedMembers = teamMembers.filter((member) => {
      if (!member.joined_at || !member.joined) return false;
      const joinedDate = new Date(member.joined_at);
      return joinedDate >= lastWeek;
    }).length;

    return {
      total,
      activeTeams,
      totalMembers,
      averageMembersPerTeam,
      newTeams,
      recentlyJoinedMembers,
    };
  }, [teams, teamMembers]);

  if (loading) {
    return (
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {['total', 'active', 'members', 'average'].map((metric) => (
          <Card key={metric} className='animate-pulse'>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <div className='h-4 w-20 bg-muted rounded'></div>
              <div className='h-4 w-4 bg-muted rounded'></div>
            </CardHeader>
            <CardContent>
              <div className='h-8 w-16 bg-muted rounded mb-2'></div>
              <div className='h-3 w-24 bg-muted rounded'></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const activeTeamPercentage =
    metrics.total > 0
      ? Math.round((metrics.activeTeams / metrics.total) * 100)
      : 0;

  return (
    <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
      <MetricCard
        title='Total Teams'
        value={metrics.total}
        subtitle={`${metrics.newTeams} new this week`}
        trend={metrics.newTeams > 0 ? 'up' : 'neutral'}
        icon={<Layers className='h-4 w-4' />}
      />

      <MetricCard
        title='Active Teams'
        value={metrics.activeTeams}
        subtitle={`${activeTeamPercentage}% have members`}
        variant={metrics.activeTeams > 0 ? 'success' : 'default'}
        icon={<Target className='h-4 w-4' />}
      />

      <MetricCard
        title='Team Members'
        value={metrics.totalMembers}
        subtitle={`${metrics.recentlyJoinedMembers} joined this week`}
        variant={metrics.totalMembers > 0 ? 'success' : 'default'}
        trend={metrics.recentlyJoinedMembers > 0 ? 'up' : 'neutral'}
        icon={<Users className='h-4 w-4' />}
      />

      <MetricCard
        title='Avg Members/Team'
        value={metrics.averageMembersPerTeam}
        subtitle='Average team size'
        variant={metrics.averageMembersPerTeam > 3 ? 'success' : 'default'}
        icon={<UserCheck className='h-4 w-4' />}
      />
    </div>
  );
}
