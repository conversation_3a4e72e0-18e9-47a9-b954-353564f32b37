'use client';

import { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import type { Task } from '@/lib/supabase/database-modules';

interface CollaboratorTasksMetricsProps {
  tasks: Task[];
  loading?: boolean;
}

export function CollaboratorTasksMetrics({
  tasks,
  loading = false,
}: CollaboratorTasksMetricsProps) {
  const metrics = useMemo(() => {
    if (loading || !tasks) {
      return {
        total: 0,
        backlog: 0,
        todo: 0,
        inProgress: 0,
        review: 0,
        completed: 0,
        overdue: 0,
        assignedToMe: 0,
      };
    }

    const now = new Date();
    const total = tasks.length;
    const backlog = tasks.filter((task) => task.status === 'backlog').length;
    const todo = tasks.filter((task) => task.status === 'todo').length;
    const inProgress = tasks.filter((task) => task.status === 'in_progress').length;
    const review = tasks.filter((task) => task.status === 'review').length;
    const completed = tasks.filter((task) => task.status === 'completed').length;
    const overdue = tasks.filter((task) => {
      if (!task.due_date) return false;
      const dueDate = new Date(task.due_date);
      return dueDate < now && task.status !== 'completed';
    }).length;
    
    // For collaborators, we'll count tasks assigned to them
    const assignedToMe = tasks.filter((task) => task.assigned_to).length;

    return {
      total,
      backlog,
      todo,
      inProgress,
      review,
      completed,
      overdue,
      assignedToMe,
    };
  }, [tasks, loading]);

  const metricCards = [
    {
      title: 'Total Tasks',
      value: metrics.total,
      description: 'All accessible tasks',
    },
    {
      title: 'Backlog',
      value: metrics.backlog,
      description: 'Tasks in backlog',
    },
    {
      title: 'To Do',
      value: metrics.todo,
      description: 'Ready to start',
    },
    {
      title: 'In Progress',
      value: metrics.inProgress,
      description: 'Currently being worked on',
    },
    {
      title: 'In Review',
      value: metrics.review,
      description: 'Under review',
    },
    {
      title: 'Completed',
      value: metrics.completed,
      description: 'Finished tasks',
    },
    {
      title: 'Overdue',
      value: metrics.overdue,
      description: 'Past due date',
    },
    {
      title: 'Assigned to Me',
      value: metrics.assignedToMe,
      description: 'Tasks I\'m working on',
    },
  ];

  if (loading) {
    return (
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4'>
        {metricCards.map((_, index) => (
          <Card key={index}>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <Skeleton className='h-4 w-20' />
            </CardHeader>
            <CardContent>
              <Skeleton className='h-8 w-12 mb-1' />
              <Skeleton className='h-3 w-24' />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4'>
      {metricCards.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>{metric.title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{metric.value}</div>
            <p className='text-xs text-muted-foreground'>{metric.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
