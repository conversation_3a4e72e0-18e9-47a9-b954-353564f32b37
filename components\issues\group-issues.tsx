'use client';

import { Plus } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { type FC, useRef, useMemo, memo } from 'react';
import { type DropTargetMonitor, useDrop } from 'react-dnd';

import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIssues } from '@/hooks/use-db';
import { useCreateIssueStore } from '@/lib/store/create-issue-store';
import { useViewStore } from '@/lib/store/view-store';
import { StatusIcon } from '@/lib/constants/status';
import type { Issue } from '@/lib/supabase/database-modules';
import { cn } from '@/lib/utils';

import { IssueDragType, IssueGrid } from './issue-grid';
import { IssueLine } from './issue-line';

interface StatusOption {
  id: string;
  name: string;
  color: string;
}

interface GroupIssuesProps {
  status: StatusOption;
  issues: Issue[];
  count: number;
}

export const GroupIssues = memo(function GroupIssues({
  status,
  issues,
  count,
}: GroupIssuesProps) {
  const { viewType } = useViewStore();
  const isViewTypeGrid = viewType === 'grid';
  const { openModal } = useCreateIssueStore();

  // Memoize sorted issues to prevent unnecessary re-sorting
  const sortedIssues = useMemo(() => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return [...issues].sort((a, b) => {
      const aPriority =
        priorityOrder[
          a.priority?.name?.toLowerCase() as keyof typeof priorityOrder
        ] || 0;
      const bPriority =
        priorityOrder[
          b.priority?.name?.toLowerCase() as keyof typeof priorityOrder
        ] || 0;
      return bPriority - aPriority;
    });
  }, [issues]);

  return (
    <div
      className={cn(
        'bg-conainer',
        isViewTypeGrid
          ? 'overflow-hidden h-full flex-shrink-0 w-[348px] flex flex-col'
          : ''
      )}
    >
      <div
        className={cn(
          'sticky top-0 z-10 bg-container w-full',
          isViewTypeGrid ? ' h-[50px]' : 'h-10'
        )}
      >
        <div
          className={cn(
            'w-full h-full flex items-center justify-between',
            isViewTypeGrid ? 'px-3' : 'px-6'
          )}
          style={{
            backgroundColor: isViewTypeGrid
              ? `${status.color}10`
              : `${status.color}08`,
          }}
        >
          <div className='flex items-center gap-2'>
            <StatusIcon statusId={status.id} />
            <span className='text-sm font-medium'>{status.name}</span>
            <span className='text-sm text-muted-foreground'>{count}</span>
          </div>

          <Button
            className='size-6'
            size='icon'
            variant='ghost'
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              openModal();
            }}
          >
            <Plus className='size-4' />
          </Button>
        </div>
      </div>

      {viewType === 'list' ? (
        <div className='space-y-0'>
          {sortedIssues.map((issue) => (
            <IssueLine key={issue.id} issue={issue} layoutId={true} />
          ))}
        </div>
      ) : (
        <IssueGridList issues={issues} status={status} />
      )}
    </div>
  );
});

const IssueGridList: FC<{ issues: Issue[]; status: StatusOption }> = memo(
  ({ issues, status }) => {
    const ref = useRef<HTMLDivElement>(null);
    const { updateIssue } = useIssues();

    // Set up drop functionality to accept only issue items.
    const [{ isOver }, drop] = useDrop(() => ({
      accept: IssueDragType,
      drop: (item: Issue, monitor: DropTargetMonitor) => {
        if (!monitor.didDrop() && item.status_id !== status.id) {
          updateIssue(item.id, { status_id: status.id })
            .then(() => {
              console.log('Issue status updated successfully');
            })
            .catch((error) => {
              console.error('Failed to update issue status:', error);
            });
        }
      },
      collect: (monitor) => ({
        isOver: !!monitor.isOver(),
      }),
    }));
    drop(ref);

    // Sort issues by priority (high to low)
    const sortedIssues = [...issues].sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const aPriority =
        priorityOrder[
          a.priority?.name?.toLowerCase() as keyof typeof priorityOrder
        ] || 0;
      const bPriority =
        priorityOrder[
          b.priority?.name?.toLowerCase() as keyof typeof priorityOrder
        ] || 0;
      return bPriority - aPriority;
    });

    return (
      <ScrollArea className='flex-1 h-full'>
        <div
          ref={ref}
          className='p-2 space-y-2 bg-zinc-50/50 dark:bg-zinc-900/50 relative'
        >
          <AnimatePresence>
            {isOver && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.1 }}
                className='fixed top-0 left-0 right-0 bottom-0 z-10 flex items-center justify-center pointer-events-none bg-background/90'
                style={{
                  width: ref.current?.getBoundingClientRect().width || '100%',
                  height: ref.current?.getBoundingClientRect().height || '100%',
                  transform: `translate(${ref.current?.getBoundingClientRect().left || 0}px, ${ref.current?.getBoundingClientRect().top || 0}px)`,
                }}
              >
                <div className='bg-background border border-border p-3 shadow-md max-w-[90%]'>
                  <p className='text-sm font-medium text-center'>
                    Board ordered by priority
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          {sortedIssues.map((issue) => (
            <IssueGrid key={issue.id} issue={issue} />
          ))}
        </div>
      </ScrollArea>
    );
  }
);
