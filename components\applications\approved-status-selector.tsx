'use client';

import { Check, Clock, X } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Application } from '@/lib/supabase/database-modules';
import { cn } from '@/lib/utils';

interface ApprovedStatusSelectorProps {
  application: Application;
  onApprovedChange: (
    applicationId: number,
    approved: 'accepted' | 'reviewing' | 'notAccepted'
  ) => void;
}

const ApprovedStatusIcon = ({
  status,
}: {
  status: 'accepted' | 'reviewing' | 'notAccepted';
}) => {
  if (status === 'accepted') {
    return <Check className='h-4 w-4 text-green-600' />;
  }
  if (status === 'notAccepted') {
    return <X className='h-4 w-4 text-red-600' />;
  }
  return <Clock className='h-4 w-4 text-yellow-600' />;
};

const getStatusText = (
  status: 'accepted' | 'reviewing' | 'notAccepted'
): string => {
  if (status === 'accepted') return 'Accepted';
  if (status === 'notAccepted') return 'Not Accepted';
  return 'Reviewing';
};

const getStatusColor = (
  status: 'accepted' | 'reviewing' | 'notAccepted'
): string => {
  if (status === 'accepted') return 'text-green-600';
  if (status === 'notAccepted') return 'text-red-600';
  return 'text-yellow-600';
};

export function ApprovedStatusSelector({
  application,
  onApprovedChange,
}: ApprovedStatusSelectorProps) {
  const [isUpdating, setIsUpdating] = useState(false);

  // Check if the approved status can be changed (only reviewing applications can be changed)
  const canChangeStatus = application.approved === 'reviewing';

  const handleStatusChange = (
    newStatus: 'accepted' | 'reviewing' | 'notAccepted'
  ) => {
    if (isUpdating || !canChangeStatus) return;

    setIsUpdating(true);
    onApprovedChange(application.id, newStatus);

    // Reset updating state after a short delay
    setTimeout(() => {
      setIsUpdating(false);
    }, 1000);
  };

  // If status cannot be changed, render as a static display
  if (!canChangeStatus) {
    return (
      <div
        className={cn(
          'h-8 px-2 flex items-center gap-2 rounded-md',
          getStatusColor(application.approved)
        )}
      >
        <ApprovedStatusIcon status={application.approved} />
        <span className='text-sm font-medium'>
          {getStatusText(application.approved)}
        </span>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className={cn(
            'h-8 px-2 flex items-center gap-2',
            getStatusColor(application.approved),
            isUpdating && 'opacity-50 cursor-not-allowed'
          )}
          disabled={isUpdating}
        >
          <ApprovedStatusIcon status={application.approved} />
          <span className='text-sm font-medium'>
            {getStatusText(application.approved)}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start'>
        <DropdownMenuItem
          onClick={() => handleStatusChange('reviewing')}
          className='flex items-center gap-2'
        >
          <Clock className='h-4 w-4 text-yellow-600' />
          <span>Reviewing</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleStatusChange('accepted')}
          className='flex items-center gap-2'
        >
          <Check className='h-4 w-4 text-green-600' />
          <span>Accepted</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleStatusChange('notAccepted')}
          className='flex items-center gap-2'
        >
          <X className='h-4 w-4 text-red-600' />
          <span>Not Accepted</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
