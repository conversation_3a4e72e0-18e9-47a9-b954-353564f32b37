-- Issues Management System Database Migration
-- This script creates the complete database schema for the issues management system
-- Run this script in your Supabase SQL editor
--
-- IMPORTANT: Run this entire script in one go in the Supabase SQL Editor

-- ============================================================================
-- STEP 1: Update profiles table to match expected schema
-- ============================================================================

-- Add missing columns to profiles table if they don't exist
DO $$
BEGIN
    -- Add name column (using full_name if it exists)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'name') THEN
        ALTER TABLE profiles ADD COLUMN name TEXT;
        -- Copy data from full_name if it exists
        UPDATE profiles SET name = full_name WHERE full_name IS NOT NULL;
        -- Make name NOT NULL after copying data
        ALTER TABLE profiles ALTER COLUMN name SET NOT NULL;
    END IF;

    -- Add status column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'status') THEN
        ALTER TABLE profiles ADD COLUMN status TEXT CHECK (status IN ('online', 'offline', 'away')) DEFAULT 'offline';
    END IF;

    -- Add joined_date column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'joined_date') THEN
        ALTER TABLE profiles ADD COLUMN joined_date TIMESTAMPTZ DEFAULT NOW();
    END IF;

    -- Add created_at column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'created_at') THEN
        ALTER TABLE profiles ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW();
    END IF;

    -- Ensure email is NOT NULL
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'email' AND is_nullable = 'YES') THEN
        UPDATE profiles SET email = username || '@example.com' WHERE email IS NULL;
        ALTER TABLE profiles ALTER COLUMN email SET NOT NULL;
    END IF;
END $$;

-- ============================================================================
-- STEP 2: Create reference tables
-- ============================================================================

-- Create Status table
CREATE TABLE IF NOT EXISTS status (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  icon_name TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create Priorities table
CREATE TABLE IF NOT EXISTS priorities (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  icon_name TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create Labels table
CREATE TABLE IF NOT EXISTS labels (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- STEP 3: Create main entity tables
-- ============================================================================

-- Create Teams table
CREATE TABLE IF NOT EXISTS teams (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  icon TEXT,
  color TEXT,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create Team Members Junction Table
CREATE TABLE IF NOT EXISTS team_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  team_id TEXT REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  joined BOOLEAN DEFAULT false,
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(team_id, user_id)
);

-- Note: We'll use the existing projects table structure and modify it if needed
-- Check if we need to modify the existing projects table
DO $$
BEGIN
    -- Add missing columns to projects table if they don't exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects' AND table_schema = 'public') THEN
        -- Add icon column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'icon') THEN
            ALTER TABLE projects ADD COLUMN icon TEXT;
        END IF;

        -- Add percent_complete column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'percent_complete') THEN
            ALTER TABLE projects ADD COLUMN percent_complete INTEGER DEFAULT 0 CHECK (percent_complete >= 0 AND percent_complete <= 100);
        END IF;

        -- Add target_date column if missing (using due_date if it exists)
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'target_date') THEN
            ALTER TABLE projects ADD COLUMN target_date DATE;
            -- Copy from due_date if it exists
            IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'due_date') THEN
                UPDATE projects SET target_date = due_date::date WHERE due_date IS not NULL;
            END IF;
        END IF;

        -- Add lead_id column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'lead_id') THEN
            ALTER TABLE projects ADD COLUMN lead_id UUID REFERENCES profiles(id);
        END IF;

        -- Add status_id column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'status_id') THEN
            ALTER TABLE projects ADD COLUMN status_id TEXT;
        END IF;

        -- Add priority_id column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'priority_id') THEN
            ALTER TABLE projects ADD COLUMN priority_id TEXT;
        END IF;

        -- Add health_id column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'health_id') THEN
            ALTER TABLE projects ADD COLUMN health_id TEXT CHECK (health_id IN ('no-update', 'off-track', 'on-track', 'at-risk'));
        END IF;
    ELSE
        -- Create projects table if it doesn't exist
        CREATE TABLE projects (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          icon TEXT,
          percent_complete INTEGER DEFAULT 0 CHECK (percent_complete >= 0 AND percent_complete <= 100),
          start_date DATE,
          target_date DATE,
          lead_id UUID REFERENCES profiles(id),
          status_id TEXT,
          priority_id TEXT,
          health_id TEXT CHECK (health_id IN ('no-update', 'off-track', 'on-track', 'at-risk')),
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
    END IF;
END $$;

-- Create Cycles table
CREATE TABLE IF NOT EXISTS cycles (
  id TEXT PRIMARY KEY,
  number INTEGER NOT NULL,
  name TEXT NOT NULL,
  team_id TEXT REFERENCES teams(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- STEP 4: Create Issues table and junction tables
-- ============================================================================

-- Create Issues table (Main Entity)
CREATE TABLE IF NOT EXISTS issues (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  identifier TEXT NOT NULL UNIQUE,
  title TEXT NOT NULL,
  description TEXT DEFAULT '',
  status_id TEXT REFERENCES status(id) NOT NULL,
  assignee_id UUID REFERENCES profiles(id),
  priority_id TEXT REFERENCES priorities(id) NOT NULL,
  project_id TEXT REFERENCES projects(id),
  cycle_id TEXT REFERENCES cycles(id),
  parent_issue_id UUID REFERENCES issues(id),
  rank TEXT NOT NULL, -- LexoRank for ordering
  due_date DATE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id) NOT NULL
);

-- Create Issue Labels Junction Table
CREATE TABLE IF NOT EXISTS issue_labels (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  issue_id UUID REFERENCES issues(id) ON DELETE CASCADE,
  label_id TEXT REFERENCES labels(id) ON DELETE CASCADE,
  UNIQUE(issue_id, label_id)
);

-- Create Project Teams Junction Table
CREATE TABLE IF NOT EXISTS project_teams (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id TEXT REFERENCES projects(id) ON DELETE CASCADE,
  team_id TEXT REFERENCES teams(id) ON DELETE CASCADE,
  UNIQUE(project_id, team_id)
);

-- ============================================================================
-- STEP 5: Add foreign key constraints for projects
-- ============================================================================

-- Add foreign key constraints for projects (if they don't exist)
DO $$
BEGIN
    -- Add status foreign key
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'fk_projects_status' AND table_name = 'projects') THEN
        ALTER TABLE projects ADD CONSTRAINT fk_projects_status FOREIGN KEY (status_id) REFERENCES status(id);
    END IF;

    -- Add priority foreign key
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'fk_projects_priority' AND table_name = 'projects') THEN
        ALTER TABLE projects ADD CONSTRAINT fk_projects_priority FOREIGN KEY (priority_id) REFERENCES priorities(id);
    END IF;
END $$;

-- ============================================================================
-- STEP 6: Create indexes for performance
-- ============================================================================

-- Issues table indexes
CREATE INDEX IF NOT EXISTS idx_issues_status_id ON issues(status_id);
CREATE INDEX IF NOT EXISTS idx_issues_assignee_id ON issues(assignee_id);
CREATE INDEX IF NOT EXISTS idx_issues_priority_id ON issues(priority_id);
CREATE INDEX IF NOT EXISTS idx_issues_project_id ON issues(project_id);
CREATE INDEX IF NOT EXISTS idx_issues_cycle_id ON issues(cycle_id);
CREATE INDEX IF NOT EXISTS idx_issues_parent_issue_id ON issues(parent_issue_id);
CREATE INDEX IF NOT EXISTS idx_issues_created_at ON issues(created_at);
CREATE INDEX IF NOT EXISTS idx_issues_rank ON issues(rank);
CREATE INDEX IF NOT EXISTS idx_issues_identifier ON issues(identifier);

-- Text search index for issues
CREATE INDEX IF NOT EXISTS idx_issues_search ON issues USING gin(to_tsvector('english', title || ' ' || description));

-- Team members indexes
CREATE INDEX IF NOT EXISTS idx_team_members_team_id ON team_members(team_id);
CREATE INDEX IF NOT EXISTS idx_team_members_user_id ON team_members(user_id);

-- Issue labels indexes
CREATE INDEX IF NOT EXISTS idx_issue_labels_issue_id ON issue_labels(issue_id);
CREATE INDEX IF NOT EXISTS idx_issue_labels_label_id ON issue_labels(label_id);

-- Project teams indexes
CREATE INDEX IF NOT EXISTS idx_project_teams_project_id ON project_teams(project_id);
CREATE INDEX IF NOT EXISTS idx_project_teams_team_id ON project_teams(team_id);

-- ============================================================================
-- STEP 7: Create database functions and triggers
-- ============================================================================

-- Update Timestamp Function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply Update Triggers (only if they don't exist)
DO $$
BEGIN
    -- Profiles trigger
    IF NOT EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'update_profiles_updated_at') THEN
        CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Teams trigger
    IF NOT EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'update_teams_updated_at') THEN
        CREATE TRIGGER update_teams_updated_at BEFORE UPDATE ON teams
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Projects trigger
    IF NOT EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'update_projects_updated_at') THEN
        CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Cycles trigger
    IF NOT EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'update_cycles_updated_at') THEN
        CREATE TRIGGER update_cycles_updated_at BEFORE UPDATE ON cycles
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Issues trigger
    IF NOT EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'update_issues_updated_at') THEN
        CREATE TRIGGER update_issues_updated_at BEFORE UPDATE ON issues
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- ============================================================================
-- STEP 8: Enable Row Level Security (RLS) and create policies
-- ============================================================================

-- Profiles Table RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist and recreate them
DROP POLICY IF EXISTS "Users can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;

CREATE POLICY "Users can view all profiles" ON profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Teams Table RLS
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view all teams" ON teams;
DROP POLICY IF EXISTS "Admins can manage teams" ON teams;

CREATE POLICY "Users can view all teams" ON teams
  FOR SELECT USING (true);

CREATE POLICY "Admins can manage teams" ON teams
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

-- Team Members Table RLS
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view team memberships" ON team_members;
DROP POLICY IF EXISTS "Users can manage own memberships" ON team_members;
DROP POLICY IF EXISTS "Admins can manage all memberships" ON team_members;

CREATE POLICY "Users can view team memberships" ON team_members
  FOR SELECT USING (true);

CREATE POLICY "Users can manage own memberships" ON team_members
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all memberships" ON team_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

-- Projects Table RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view all projects" ON projects;
DROP POLICY IF EXISTS "Project leads and admins can update projects" ON projects;
DROP POLICY IF EXISTS "Admins can manage projects" ON projects;
DROP POLICY IF EXISTS "Admins can delete projects" ON projects;

CREATE POLICY "Users can view all projects" ON projects
  FOR SELECT USING (true);

CREATE POLICY "Project leads and admins can update projects" ON projects
  FOR UPDATE USING (
    auth.uid() = lead_id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

CREATE POLICY "Admins can manage projects" ON projects
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

CREATE POLICY "Admins can delete projects" ON projects
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

-- Issues Table RLS
ALTER TABLE issues ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view all issues" ON issues;
DROP POLICY IF EXISTS "Users can create issues" ON issues;
DROP POLICY IF EXISTS "Users can update own or assigned issues" ON issues;
DROP POLICY IF EXISTS "Users can delete own issues, admins can delete any" ON issues;

CREATE POLICY "Users can view all issues" ON issues
  FOR SELECT USING (true);

CREATE POLICY "Users can create issues" ON issues
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update own or assigned issues" ON issues
  FOR UPDATE USING (
    auth.uid() = created_by OR
    auth.uid() = assignee_id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

CREATE POLICY "Users can delete own issues, admins can delete any" ON issues
  FOR DELETE USING (
    auth.uid() = created_by OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'Admin'
    )
  );

-- Reference Tables RLS (Status, Priorities, Labels)
ALTER TABLE status ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view status" ON status;
DROP POLICY IF EXISTS "Admins can manage status" ON status;
CREATE POLICY "Users can view status" ON status FOR SELECT USING (true);
CREATE POLICY "Admins can manage status" ON status FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);

ALTER TABLE priorities ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view priorities" ON priorities;
DROP POLICY IF EXISTS "Admins can manage priorities" ON priorities;
CREATE POLICY "Users can view priorities" ON priorities FOR SELECT USING (true);
CREATE POLICY "Admins can manage priorities" ON priorities FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);

ALTER TABLE labels ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view labels" ON labels;
DROP POLICY IF EXISTS "Admins can manage labels" ON labels;
CREATE POLICY "Users can view labels" ON labels FOR SELECT USING (true);
CREATE POLICY "Admins can manage labels" ON labels FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);

-- Junction Tables RLS
-- Issue Labels
ALTER TABLE issue_labels ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view issue labels" ON issue_labels;
DROP POLICY IF EXISTS "Users can manage issue labels" ON issue_labels;
CREATE POLICY "Users can view issue labels" ON issue_labels FOR SELECT USING (true);
CREATE POLICY "Users can manage issue labels" ON issue_labels FOR ALL USING (
  EXISTS (
    SELECT 1 FROM issues
    WHERE id = issue_id AND (created_by = auth.uid() OR assignee_id = auth.uid())
  ) OR
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);

-- Project Teams
ALTER TABLE project_teams ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view project teams" ON project_teams;
DROP POLICY IF EXISTS "Admins can manage project teams" ON project_teams;
CREATE POLICY "Users can view project teams" ON project_teams FOR SELECT USING (true);
CREATE POLICY "Admins can manage project teams" ON project_teams FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'Admin')
);

-- ============================================================================
-- STEP 9: Insert sample data
-- ============================================================================

-- Insert Status Data
INSERT INTO status (id, name, color, icon_name, sort_order) VALUES
('in-progress', 'In Progress', '#facc15', 'InProgressIcon', 1),
('technical-review', 'Technical Review', '#22c55e', 'TechnicalReviewIcon', 2),
('completed', 'Completed', '#8b5cf6', 'CompletedIcon', 3),
('paused', 'Paused', '#0ea5e9', 'PausedIcon', 4),
('to-do', 'Todo', '#f97316', 'ToDoIcon', 5),
('backlog', 'Backlog', '#ec4899', 'BacklogIcon', 6)
ON CONFLICT (id) DO NOTHING;

-- Insert Priorities Data
INSERT INTO priorities (id, name, icon_name, sort_order) VALUES
('no-priority', 'No priority', 'NoPriorityIcon', 1),
('urgent', 'Urgent', 'UrgentPriorityIcon', 2),
('high', 'High', 'HighPriorityIcon', 3),
('medium', 'Medium', 'MediumPriorityIcon', 4),
('low', 'Low', 'LowPriorityIcon', 5)
ON CONFLICT (id) DO NOTHING;

-- Insert Labels Data
INSERT INTO labels (id, name, color) VALUES
('ui', 'UI Enhancement', 'purple'),
('bug', 'Bug', 'red'),
('feature', 'Feature', 'green'),
('documentation', 'Documentation', 'blue'),
('refactor', 'Refactor', 'yellow'),
('performance', 'Performance', 'orange'),
('design', 'Design', 'pink'),
('security', 'Security', 'gray'),
('accessibility', 'Accessibility', 'indigo'),
('testing', 'Testing', 'teal'),
('internationalization', 'Internationalization', 'cyan')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Verify the migration by checking if all tables exist
SELECT
  table_name,
  CASE
    WHEN table_name IN ('profiles', 'status', 'priorities', 'labels', 'teams', 'team_members', 'projects', 'cycles', 'issues', 'issue_labels', 'project_teams')
    THEN '✅ Created'
    ELSE '❌ Missing'
  END as status
FROM information_schema.tables
WHERE table_schema = 'public'
  AND table_name IN ('profiles', 'status', 'priorities', 'labels', 'teams', 'team_members', 'projects', 'cycles', 'issues', 'issue_labels', 'project_teams')
ORDER BY table_name;