'use client';
import { Plus } from 'lucide-react';
import Link from 'next/link';
import { ProjectsMetrics } from '@/components/projects/projects-metrics';
import Projects from '@/components/tables/projects/projects';
import { Button } from '@/components/ui/button';
import { useProjects } from '@/hooks/use-db';

export default function Page() {
  const { projects, loading } = useProjects();

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Projects</p>
        <Link href='/admin/projects/new'>
          <Button size='sm' className='h-8'>
            <Plus className='h-4 w-4 mr-2' />
            New Project
          </Button>
        </Link>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <ProjectsMetrics projects={projects} loading={loading} />
        <Projects />
      </section>
    </main>
  );
}

// now that we have the right hooks for the projects update the files in the projects in the components folder to use the hooks and make the update work and be functioal also add a btn at the end to view project i have created a page in the prokjects [id]/page populate that page to match the picture i have attached with real data using the hooks put all this processes in steps and implement them now
