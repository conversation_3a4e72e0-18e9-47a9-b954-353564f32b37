'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Shield, FileText, CheckCircle, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { usePortfolio } from '@/hooks/use-db';

export default function CollaboratorNDAPage() {
  const router = useRouter();
  const { portfolioData, loading, signNDA } = usePortfolio();
  const [hasRead, setHasRead] = useState(false);
  const [isAgreeing, setIsAgreeing] = useState(false);

  const handleSignNDA = () => {
    if (!hasRead) {
      toast.error('Please confirm that you have read and understood the NDA');
      return;
    }

    setIsAgreeing(true);

    signNDA()
      .then(() => {
        toast.success('NDA signed successfully!');
        router.push('/collaborator');
      })
      .catch((error) => {
        toast.error(`Failed to sign NDA: ${error.message}`);
      })
      .finally(() => {
        setIsAgreeing(false);
      });
  };

  if (loading) {
    return (
      <div className='h-full flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading...</p>
        </div>
      </div>
    );
  }

  // If NDA is already signed, show confirmation
  if (portfolioData?.is_nda_signed) {
    return (
      <main className='h-full flex items-center justify-center p-6'>
        <Card className='max-w-md w-full'>
          <CardHeader className='text-center'>
            <div className='mx-auto w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4'>
              <CheckCircle className='w-6 h-6 text-green-600 dark:text-green-400' />
            </div>
            <CardTitle>NDA Already Signed</CardTitle>
            <CardDescription>
              You have already signed the Non-Disclosure Agreement
            </CardDescription>
          </CardHeader>
          <CardContent className='text-center'>
            <p className='text-sm text-muted-foreground mb-4'>
              Signed on:{' '}
              {portfolioData.nda_signed_date
                ? new Date(portfolioData.nda_signed_date).toLocaleDateString()
                : 'Previously signed'}
            </p>
            <Button
              onClick={() => router.push('/collaborator')}
              className='w-full'
            >
              Continue to Dashboard
            </Button>
          </CardContent>
        </Card>
      </main>
    );
  }

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center space-x-2'>
          <Shield className='h-4 w-4' />
          <p className='font-medium text-sm'>Non-Disclosure Agreement</p>
        </div>
      </header>

      <section className='flex-1 p-6'>
        <div className='max-w-4xl mx-auto space-y-6'>
          {/* Warning Notice */}
          <Card className='border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950'>
            <CardHeader>
              <div className='flex items-center space-x-2'>
                <AlertTriangle className='h-5 w-5 text-yellow-600 dark:text-yellow-400' />
                <CardTitle className='text-yellow-800 dark:text-yellow-200'>
                  Required Agreement
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className='text-yellow-800 dark:text-yellow-200'>
                You must read and agree to this Non-Disclosure Agreement before
                accessing your collaborator dashboard and project tools.
              </p>
            </CardContent>
          </Card>

          {/* NDA Content */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <FileText className='h-5 w-5' />
                <span>Non-Disclosure Agreement</span>
              </CardTitle>
              <CardDescription>
                Please read the following agreement carefully
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className='h-96 w-full border rounded-lg p-4'>
                <div className='space-y-4 text-sm'>
                  <h3 className='font-semibold text-base'>
                    NON-DISCLOSURE AGREEMENT
                  </h3>

                  <p>
                    This Non-Disclosure Agreement is entered into as of the
                    Effective Date and between:
                  </p>

                  <div className='space-y-2'>
                    <div>
                      <p className='font-semibold'>Disclosing Party</p>
                      <p className='text-lg font-bold'>Thehuefactory</p>
                      <p className='text-muted-foreground'>Creative Agency</p>
                      <p>Accra, Greater Accra, Ghana.</p>
                    </div>

                    <div>
                      <p className='font-semibold'>Receiving Party</p>
                      <p className='text-lg font-bold'>
                        {portfolioData?.full_name || 'Collaborator'}
                      </p>
                      <p className='text-muted-foreground'>Collaborator</p>
                      <p>Accra, Greater Accra, Ghana.</p>
                    </div>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>1. Purpose</h4>
                    <p>
                      The Disclosing Party intends to share certain confidential
                      and proprietary information with the Receiving Party for
                      the purpose of exploring, discussing, or engaging in a
                      potential business relationship concerning the development
                      of creative projects ("Purpose"). Both parties agree that
                      the disclosure and protection of such Confidential
                      Information is critical for the success of this
                      relationship.
                    </p>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>
                      2. Confidential Information
                    </h4>
                    <p>
                      For the purposes of this Agreement, "Confidential
                      Information" includes but is not limited to:
                    </p>
                    <ul className='list-disc list-inside ml-4 space-y-1'>
                      <li>Creative concepts, strategies, and plans.</li>
                      <li>
                        Technical and business information, including
                        intellectual property.
                      </li>
                      <li>Pricing, project quotes, and budgets.</li>
                      <li>Client Lists and contact information.</li>
                      <li>Deliverables drafts and final project materials.</li>
                      <li>
                        Any other proprietary or sensitive information that is
                        disclosed or becomes known through the course of
                        collaboration.
                      </li>
                    </ul>

                    <p className='mt-2'>
                      Confidential Information <strong>does not include</strong>{' '}
                      information that:
                    </p>
                    <ul className='list-disc list-inside ml-4 space-y-1'>
                      <li>
                        Is publicly available at the time of disclosure or
                        subsequently becomes publicly available through no
                        breach of this Agreement by the Receiving Party;
                      </li>
                      <li>
                        Is already in the possession of the Receiving Party
                        without obligation of confidentiality at the time of
                        disclosure;
                      </li>
                      <li>
                        Is lawfully obtained from a third party without breach
                        of confidentiality obligations;
                      </li>
                      <li>
                        Is independently developed by the Receiving Party
                        without the use of the Confidential Information.
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>
                      3. Obligations of the Receiving Party
                    </h4>
                    <p>The Receiving Party agrees to:</p>
                    <ul className='list-disc list-inside ml-4 space-y-1'>
                      <li>
                        Maintain all Confidential Information in strict
                        confidence and not disclose it to any third party
                        without the prior written consent of the Disclosing
                        Party;
                      </li>
                      <li>
                        Use the Confidential Information solely for the Purpose
                        stated in this Agreement;
                      </li>
                      <li>
                        Take all reasonable precautions to protect the
                        confidentiality of the information and prevent
                        unauthorized use or disclosure.
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>4. Term</h4>
                    <p>
                      The obligations under this Agreement shall remain in
                      effect for a period of 2 years from the Effective Date or
                      until such time that the Confidential Information no
                      longer qualifies as confidential under the terms of this
                      Agreement, whichever occurs first.
                    </p>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>
                      5. Return or Destruction of Confidential Information
                    </h4>
                    <p>
                      This Agreement is effective as of the Effective Date and
                      shall remain in effect for a period of 2 years from the
                      date of disclosure of the Confidential Information. The
                      obligations of confidentiality shall survive termination
                      of this Agreement.
                    </p>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>6. No License</h4>
                    <p>
                      Nothing in this Agreement grants the Collaborator any
                      right or license to use the Confidential Information for
                      any purpose other than the Purpose stated herein.
                    </p>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>7. Remedies</h4>
                    <p>
                      Both parties acknowledge that any breach of this Agreement
                      may cause irreparable harm to the Disclosing Party and
                      agree that the Disclosing Party shall have the right to
                      seek injunctive relief, specific performance, or other
                      equitable remedies to enforce this Agreement, in addition
                      to any other rights or remedies available at law.
                    </p>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>
                      8. No Obligation to Proceed
                    </h4>
                    <p>
                      Nothing in this Agreement obligates either party to enter
                      into any further agreement, business relationship, or
                      collaboration.
                    </p>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>9. Governing Law</h4>
                    <p>
                      This Agreement shall be governed by and construed in
                      accordance with the laws of the Republic of Ghana, without
                      regard to its conflict of law principles.
                    </p>
                  </div>

                  <div>
                    <h4 className='font-semibold text-lg'>
                      10. Entire Agreement
                    </h4>
                    <p>
                      This Agreement constitutes the entire understanding
                      between the parties regarding the Confidential Information
                      and supersedes all prior discussions, agreements, or
                      understandings, whether written or oral.
                    </p>
                  </div>

                  <div className='mt-6'>
                    <p className='font-semibold'>
                      IN WITNESS WHEREOF, the Parties have executed this
                      Non-Disclosure Agreement as of the Effective Date.
                    </p>
                  </div>

                  <div className='grid md:grid-cols-2 gap-4 mt-6'>
                    <div className='space-y-2'>
                      <p className='font-semibold text-lg'>Agency</p>
                      <p>Name: Ekow Ekuma Hammond</p>
                      <p>Title: Chief Executive Officer</p>
                      <p>Date: {new Date().toLocaleDateString()}</p>
                    </div>
                    <div className='space-y-2'>
                      <p className='font-semibold text-lg'>Collaborator</p>
                      <p>Name: {portfolioData?.full_name || 'Collaborator'}</p>
                      <p>Date: {new Date().toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Agreement Section */}
          <Card>
            <CardContent className='pt-6'>
              <div className='space-y-4'>
                <div className='flex items-start space-x-3'>
                  <Checkbox
                    id='nda-agreement'
                    checked={hasRead}
                    onCheckedChange={(checked) =>
                      setHasRead(checked as boolean)
                    }
                  />
                  <label
                    htmlFor='nda-agreement'
                    className='text-sm leading-relaxed cursor-pointer'
                  >
                    I have read and understood the Non-Disclosure Agreement
                    above, and I agree to be bound by all its terms and
                    conditions.
                  </label>
                </div>

                <Button
                  onClick={handleSignNDA}
                  disabled={!hasRead || isAgreeing}
                  className='w-full'
                  size='lg'
                >
                  {isAgreeing ? 'Signing...' : 'Sign NDA and Continue'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </main>
  );
}
