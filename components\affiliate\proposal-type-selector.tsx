'use client';

import { CheckIcon } from 'lucide-react';
import { useId, useState } from 'react';
import { 
  Palette, 
  Globe, 
  Smartphone, 
  Sparkles 
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

type WorkTypes = 'Graphic Design' | 'Website Development' | 'App Development' | 'Brand Development';

interface ProposalTypeOption {
  id: WorkTypes;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const proposalTypeOptions: ProposalTypeOption[] = [
  {
    id: 'Graphic Design',
    name: 'Graphic Design',
    icon: Palette,
    description: 'Visual design, logos, branding materials',
  },
  {
    id: 'Website Development',
    name: 'Website Development',
    icon: Globe,
    description: 'Web applications, websites, landing pages',
  },
  {
    id: 'App Development',
    name: 'App Development',
    icon: Smartphone,
    description: 'Mobile apps, desktop applications',
  },
  {
    id: 'Brand Development',
    name: 'Brand Development',
    icon: Sparkles,
    description: 'Brand strategy, identity, positioning',
  },
];

interface ProposalTypeSelectorProps {
  value: WorkTypes | '';
  onChange: (proposalType: WorkTypes) => void;
  placeholder?: string;
}

export function ProposalTypeSelector({ 
  value, 
  onChange, 
  placeholder = 'Select proposal type...' 
}: ProposalTypeSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);

  const handleProposalTypeChange = (proposalType: WorkTypes) => {
    setOpen(false);
    onChange(proposalType);
  };

  const selectedOption = proposalTypeOptions.find(option => option.id === value);

  return (
    <div className='w-full'>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='w-full flex items-center justify-between gap-2 h-10'
            variant='outline'
            role='combobox'
            aria-expanded={open}
          >
            <div className='flex items-center gap-2'>
              {selectedOption ? (
                <>
                  <selectedOption.icon className='h-4 w-4' />
                  <span>{selectedOption.name}</span>
                </>
              ) : (
                <span className='text-muted-foreground'>{placeholder}</span>
              )}
            </div>
            <svg
              className={`h-4 w-4 transition-transform ${open ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='w-full min-w-[var(--radix-popper-anchor-width)] p-0'
          align='start'
        >
          <Command>
            <CommandInput placeholder='Search proposal types...' />
            <CommandList>
              <CommandEmpty>No proposal type found.</CommandEmpty>
              <CommandGroup>
                {proposalTypeOptions.map((option) => (
                  <CommandItem
                    key={option.id}
                    value={option.id}
                    onSelect={() => handleProposalTypeChange(option.id)}
                    className='flex items-center justify-between p-3'
                  >
                    <div className='flex items-start gap-3'>
                      <option.icon className='h-5 w-5 text-muted-foreground mt-0.5' />
                      <div className='flex flex-col'>
                        <span className='font-medium'>{option.name}</span>
                        <span className='text-sm text-muted-foreground'>
                          {option.description}
                        </span>
                      </div>
                    </div>
                    {value === option.id && (
                      <CheckIcon className='h-4 w-4 ml-2' />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
