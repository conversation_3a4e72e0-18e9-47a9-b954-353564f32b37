"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Eye, Folder } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

import { DatePicker } from "@/components/projects/date-picker"
import { HealthPopover } from "@/components/projects/health-popover"
import { LeadSelector } from "@/components/projects/lead-selector"
import { PrioritySelector } from "@/components/projects/priority-selector"
import { StatusWithPercent } from "@/components/projects/status-with-percent"
import { Button } from "@/components/ui/button"
import { useProjects } from "@/hooks/use-db"
import type { Project } from "@/lib/supabase/database-modules"

// Helper functions to convert database types to component types
const convertPriority = (priority: Project['priority']) => {
  if (!priority) return null
  return {
    id: priority.id,
    name: priority.name,
    icon_name: priority.icon_name || '',
    sort_order: priority.sort_order || 0,
  }
}

const convertLead = (lead: Project['lead']) => {
  if (!lead) return null
  return {
    id: lead.id,
    name: lead.full_name || lead.email,
    email: lead.email,
    avatar_url: lead.avatar_url,
  }
}

const convertHealth = (project: Project) => {
  return {
    id: project.id,
    name: project.name,
    description: project.description,
    health: project.health
      ? {
          id: project.health.id,
          name: project.health.name,
          description: project.health.description || undefined,
        }
      : undefined,
    lead: project.lead
      ? {
          id: project.lead.id,
          name: project.lead.full_name || project.lead.email,
          avatar_url: project.lead.avatar_url || undefined,
        }
      : undefined,
  }
}

const convertStatus = (status: Project['status']) => {
  if (!status) return null
  return {
    id: status.id,
    name: status.name,
    color: status.color || '#6b7280',
    sort_order: 0,
  }
}

const getPercentComplete = (status: Project['status']) => {
  if (!status) return 0
  switch (status.name?.toLowerCase()) {
    case 'completed':
    case 'done':
      return 100
    case 'in progress':
    case 'active':
      return 60
    case 'planning':
    case 'todo':
      return 20
    case 'paused':
    case 'on hold':
      return 30
    case 'cancelled':
    case 'canceled':
      return 0
    default:
      return 0
  }
}

export const createColumns = (): ColumnDef<Project>[] => {
  return [
    {
      accessorKey: "name",
      header: "Title",
      cell: ({ row }) => {
        const project = row.original
        return (
          <div className="flex items-center gap-2">
            <div className="relative">
              <div className="inline-flex size-6 bg-muted/50 items-center justify-center rounded shrink-0">
                <Folder className="size-4" />
              </div>
            </div>
            <div className="flex flex-col items-start overflow-hidden">
              <Link
                href={`/admin/projects/${project.id}`}
                className="font-medium truncate w-full hover:underline"
              >
                {project.name}
              </Link>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: "health",
      header: "Health",
      cell: ({ row }) => {
        const project = row.original
        const { updateProject } = useProjects()

        const handleHealthChange = (healthId: string) => {
          const validHealthId = healthId as
            | 'no-update'
            | 'off-track'
            | 'on-track'
            | 'at-risk'
          updateProject(project.id, { health_id: validHealthId })
            .then(() => {
              toast.success('Health status updated successfully')
            })
            .catch((error) => {
              toast.error('Failed to update health status')
              console.error('Error updating health status:', error)
            })
        }

        return (
          <HealthPopover
            project={convertHealth(project)}
            onHealthChange={handleHealthChange}
          />
        )
      },
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: ({ row }) => {
        const project = row.original
        const { updateProject } = useProjects()

        const handlePriorityChange = (priorityId: string) => {
          updateProject(project.id, { priority_id: priorityId })
            .then(() => {
              toast.success('Priority updated successfully')
            })
            .catch((error) => {
              toast.error('Failed to update priority')
              console.error('Error updating priority:', error)
            })
        }

        return (
          <PrioritySelector
            priority={convertPriority(project.priority)}
            onPriorityChange={handlePriorityChange}
          />
        )
      },
    },
    {
      accessorKey: "lead",
      header: "Lead",
      cell: ({ row }) => {
        const project = row.original
        const { updateProject } = useProjects()

        const handleLeadChange = (leadId: string) => {
          updateProject(project.id, { lead_id: leadId })
            .then(() => {
              toast.success('Lead updated successfully')
            })
            .catch((error) => {
              toast.error('Failed to update lead')
              console.error('Error updating lead:', error)
            })
        }

        return (
          <LeadSelector
            lead={convertLead(project.lead)}
            onLeadChange={handleLeadChange}
          />
        )
      },
    },
    {
      accessorKey: "target_date",
      header: "Target Date",
      cell: ({ row }) => {
        const project = row.original
        const { updateProject } = useProjects()

        const handleDateChange = (date: Date | undefined) => {
          const targetDate = date ? date.toISOString().split('T')[0] : null
          updateProject(project.id, { target_date: targetDate })
            .then(() => {
              toast.success('Target date updated successfully')
            })
            .catch((error) => {
              toast.error('Failed to update target date')
              console.error('Error updating target date:', error)
            })
        }

        return (
          <DatePicker
            date={project.target_date ? new Date(project.target_date) : undefined}
            onDateChange={handleDateChange}
          />
        )
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const project = row.original
        const { updateProject } = useProjects()

        const handleStatusChange = (statusId: string) => {
          updateProject(project.id, { status_id: statusId })
            .then(() => {
              toast.success('Status updated successfully')
            })
            .catch((error) => {
              toast.error('Failed to update status')
              console.error('Error updating status:', error)
            })
        }

        return (
          <StatusWithPercent
            status={convertStatus(project.status)}
            percentComplete={getPercentComplete(project.status)}
            onStatusChange={handleStatusChange}
          />
        )
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const project = row.original
        const router = useRouter()

        const handleViewProject = () => {
          router.push(`/admin/projects/${project.id}`)
        }

        return (
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0"
            onClick={handleViewProject}
          >
            <Eye className="h-4 w-4" />
          </Button>
        )
      },
    },
  ]
}
