'use client';

import { DataTable } from '@/components/ui/data-table';
import { useProjects } from '@/hooks/use-db';

import { createColumns } from './columns';

export default function Projects() {
  const { projects, loading, error } = useProjects();
  const columns = createColumns();

  if (error) {
    return (
      <div className='w-full p-6'>
        <div className='text-center text-red-500'>{error}</div>
      </div>
    );
  }

  return <DataTable columns={columns} data={projects} isLoading={loading} />;
}
