import Link from 'next/link';
import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export default function Home() {
  return (
    <main>
      <section className='flex h-screen flex-col items-center justify-center space-y-4'>
        <p className='font-black text-6xl md:text-9xl tracking-[-7%]'>Teams</p>
        <Link
          href={'/login'}
          className={cn(buttonVariants({ variant: 'main' }), 'w-fit')}
        >
          Login
        </Link>
      </section>
    </main>
  );
}
