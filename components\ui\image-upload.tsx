'use client';

import { useCallback, useState } from 'react';
import { Upload, X, User, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  validateImageFile,
  createFilePreview,
  revokeFilePreview,
  type ImageUploadOptions,
} from '@/lib/utils/image-upload';

interface ImageUploadProps {
  currentImageUrl?: string | null;
  onImageSelect: (file: File) => void;
  onImageRemove?: () => void;
  disabled?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  options?: Partial<ImageUploadOptions>;
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32',
};

export function ImageUpload({
  currentImageUrl,
  onImageSelect,
  onImageRemove,
  disabled = false,
  className,
  size = 'md',
  options,
}: ImageUploadProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  const handleFileSelect = useCallback(
    (file: File) => {
      try {
        // Validate the file
        validateImageFile(file, options);

        // Create preview
        if (previewUrl) {
          revokeFilePreview(previewUrl);
        }
        const newPreviewUrl = createFilePreview(file);
        setPreviewUrl(newPreviewUrl);

        // Call the callback
        onImageSelect(file);
      } catch (error) {
        toast.error(error instanceof Error ? error.message : 'Invalid file');
      }
    },
    [onImageSelect, options, previewUrl]
  );

  const handleFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        handleFileSelect(file);
      }
    },
    [handleFileSelect]
  );

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      setIsDragging(false);

      const file = event.dataTransfer.files[0];
      if (file) {
        handleFileSelect(file);
      }
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
  }, []);

  const handleRemove = useCallback(() => {
    if (previewUrl) {
      revokeFilePreview(previewUrl);
      setPreviewUrl(null);
    }
    onImageRemove?.();
  }, [previewUrl, onImageRemove]);

  const displayImageUrl = previewUrl || currentImageUrl;

  return (
    <div className={cn('relative', className)}>
      <div
        className={cn(
          'relative border-2 border-dashed border-muted-foreground/25 rounded-lg overflow-hidden transition-colors',
          sizeClasses[size],
          isDragging && 'border-primary bg-primary/5',
          disabled && 'opacity-50 cursor-not-allowed',
          !disabled && 'hover:border-muted-foreground/50 cursor-pointer'
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {displayImageUrl ? (
          <>
            <img
              src={displayImageUrl}
              alt="Profile"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
              <Upload className="w-6 h-6 text-white" />
            </div>
          </>
        ) : (
          <div className="w-full h-full flex flex-col items-center justify-center text-muted-foreground">
            <User className="w-8 h-8 mb-2" />
            <span className="text-xs text-center px-2">
              {size === 'sm' ? 'Upload' : 'Click or drag image'}
            </span>
          </div>
        )}

        <input
          type="file"
          accept="image/*"
          onChange={handleFileInputChange}
          disabled={disabled}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
        />
      </div>

      {(displayImageUrl || previewUrl) && onImageRemove && (
        <Button
          type="button"
          variant="destructive"
          size="icon"
          className="absolute -top-2 -right-2 w-6 h-6 rounded-full"
          onClick={handleRemove}
          disabled={disabled}
        >
          <X className="w-3 h-3" />
        </Button>
      )}

      {size !== 'sm' && (
        <div className="mt-2 text-xs text-muted-foreground text-center">
          <p>JPG, PNG, or WebP up to 5MB</p>
        </div>
      )}
    </div>
  );
}

interface ImageUploadWithStatusProps extends ImageUploadProps {
  isUploading?: boolean;
  uploadProgress?: number;
}

export function ImageUploadWithStatus({
  isUploading = false,
  uploadProgress,
  ...props
}: ImageUploadWithStatusProps) {
  return (
    <div className="relative">
      <ImageUpload {...props} disabled={props.disabled || isUploading} />
      
      {isUploading && (
        <div className="absolute inset-0 bg-background/80 rounded-lg flex items-center justify-center">
          <div className="flex flex-col items-center space-y-2">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span className="text-xs text-muted-foreground">
              {uploadProgress !== undefined
                ? `${Math.round(uploadProgress)}%`
                : 'Uploading...'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
