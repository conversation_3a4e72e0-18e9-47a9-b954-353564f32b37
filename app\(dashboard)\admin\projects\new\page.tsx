'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Save, X } from 'lucide-react';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

import {
  useProjects,
  useMembers,
  useStatus,
  usePriorities,
  useProjectMembers,
} from '@/hooks/use-db';
import type { CreateProjectInput } from '@/lib/supabase/database-modules';
import { LeadSelector } from '@/components/projects/lead-selector';
import { AffiliateSelector } from '@/components/projects/affiliate-selector';
import { StatusWithPercent } from '@/components/projects/status-with-percent';
import { PrioritySelector } from '@/components/projects/priority-selector';
import { DatePicker } from '@/components/projects/date-picker';
import { HealthPopover } from '@/components/projects/health-popover';
import { ProjectMembersSelector } from '@/components/projects/project-members-selector';

// Zod schema for project creation form validation
const createProjectSchema = z.object({
  name: z
    .string()
    .min(1, 'Project name is required')
    .max(255, 'Project name is too long'),
  description: z.string().optional(),
  icon: z.string().optional(),
  lead_id: z.string().optional(),
  status_id: z.string().optional(),
  priority_id: z.string().optional(),
  health_id: z
    .enum(['no-update', 'off-track', 'on-track', 'at-risk'])
    .optional(),
  start_date: z.string().optional(),
  target_date: z.string().optional(),
  percent_complete: z.number().min(0).max(100).optional(),
  is_proposed: z.boolean().optional(),
  affiliate_user: z.string().optional(),
});

interface SelectedMember {
  id: string;
  name: string;
  email: string;
  avatar_url: string | null;
  role: 'lead' | 'member' | 'viewer';
}

type CreateProjectFormData = z.infer<typeof createProjectSchema>;

export default function NewProjectPage() {
  const router = useRouter();
  const { addProject } = useProjects();
  const { members } = useMembers();
  const { status } = useStatus();
  const { priorities } = usePriorities();
  const { addProjectMember } = useProjectMembers();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedMembers, setSelectedMembers] = useState<SelectedMember[]>([]);

  const form = useForm<CreateProjectFormData>({
    resolver: zodResolver(createProjectSchema),
    defaultValues: {
      name: '',
      description: '',
      icon: '',
      lead_id: '',
      status_id: '',
      priority_id: '',
      health_id: 'on-track',
      start_date: '',
      target_date: '',
      percent_complete: 0,
      is_proposed: false,
      affiliate_user: '',
    },
  });

  const onSubmit = (data: CreateProjectFormData): Promise<void> => {
    return new Promise((resolve, reject) => {
      setIsSubmitting(true);

      const projectInput: CreateProjectInput = {
        name: data.name.trim(),
        description: data.description?.trim() || undefined,
        icon: data.icon || undefined,
        lead_id: data.lead_id || undefined,
        status_id: data.status_id || undefined,
        priority_id: data.priority_id || undefined,
        health_id: data.health_id || undefined,
        start_date: data.start_date || undefined,
        target_date: data.target_date || undefined,
        percent_complete: data.percent_complete || 0,
        is_proposed: data.is_proposed || false,
        affiliate_user: data.affiliate_user || undefined,
      };

      const createPromise = addProject(projectInput)
        .then((newProject) => {
          // Add project members if any are selected
          const memberPromises = selectedMembers.map((member) =>
            addProjectMember({
              project_id: newProject.id,
              user_id: member.id,
              role: member.role,
            })
          );

          // Wait for all members to be added
          return Promise.all(memberPromises).then(() => newProject);
        })
        .then((newProject) => {
          // Redirect to the new project detail page
          router.push(`/admin/projects/${newProject.id}`);
          resolve();
        })
        .catch((error) => {
          console.error('Failed to create project:', error);
          reject(error);
        })
        .finally(() => {
          setIsSubmitting(false);
        });

      toast.promise(createPromise, {
        loading: 'Creating project...',
        success: 'Project created successfully',
        error: (error) =>
          `Failed to create project: ${error?.message || 'Unknown error'}`,
      });
    });
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleCancel}
            className='h-8 w-8 p-0'
            disabled={isSubmitting}
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <p className='font-medium text-sm'>New Project</p>
        </div>
        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            <X className='h-4 w-4 mr-2' />
            Cancel
          </Button>
          <Button
            size='sm'
            onClick={form.handleSubmit(onSubmit)}
            disabled={isSubmitting}
          >
            <Save className='h-4 w-4 mr-2' />
            {isSubmitting ? 'Creating...' : 'Create Project'}
          </Button>
        </div>
      </header>
      <section className='flex-1 p-6'>
        <div className='max-w-4xl mx-auto'>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <Card>
                <CardHeader>
                  <CardTitle>Project Details</CardTitle>
                </CardHeader>
                <CardContent className='space-y-6'>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div className='md:col-span-2'>
                      <FormField
                        control={form.control}
                        name='name'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Project Name *</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Enter project name'
                                disabled={isSubmitting}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className='md:col-span-2'>
                      <FormField
                        control={form.control}
                        name='description'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <Textarea
                                {...field}
                                placeholder='Enter project description'
                                rows={4}
                                disabled={isSubmitting}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div>
                      <FormField
                        control={form.control}
                        name='percent_complete'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Initial Progress (%)</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type='number'
                                min='0'
                                max='100'
                                placeholder='0'
                                disabled={isSubmitting}
                                onChange={(e) =>
                                  field.onChange(Number(e.target.value))
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div>
                      <FormField
                        control={form.control}
                        name='icon'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Icon</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Enter icon name (optional)'
                                disabled={isSubmitting}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Project Settings</CardTitle>
                </CardHeader>
                <CardContent className='space-y-6'>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                      <Label>Project Lead</Label>
                      <div className='mt-2'>
                        <LeadSelector
                          lead={
                            members.find((m) => m.id === form.watch('lead_id'))
                              ? {
                                  id: form.watch('lead_id') || '',
                                  name:
                                    members.find(
                                      (m) => m.id === form.watch('lead_id')
                                    )?.full_name ||
                                    members.find(
                                      (m) => m.id === form.watch('lead_id')
                                    )?.name ||
                                    '',
                                  email:
                                    members.find(
                                      (m) => m.id === form.watch('lead_id')
                                    )?.email || '',
                                  avatar_url:
                                    members.find(
                                      (m) => m.id === form.watch('lead_id')
                                    )?.avatar_url || null,
                                }
                              : null
                          }
                          onLeadChange={(leadId) =>
                            form.setValue('lead_id', leadId)
                          }
                        />
                      </div>
                    </div>

                    <div>
                      <Label>Status</Label>
                      <div className='mt-2'>
                        <StatusWithPercent
                          status={
                            status.find((s) => s.id === form.watch('status_id'))
                              ? {
                                  id: form.watch('status_id') || '',
                                  name:
                                    status.find(
                                      (s) => s.id === form.watch('status_id')
                                    )?.name || '',
                                  color:
                                    status.find(
                                      (s) => s.id === form.watch('status_id')
                                    )?.color || '',
                                  sort_order: 0,
                                }
                              : null
                          }
                          percentComplete={form.watch('percent_complete') || 0}
                          onStatusChange={(statusId) =>
                            form.setValue('status_id', statusId)
                          }
                        />
                      </div>
                    </div>

                    <div>
                      <Label>Priority</Label>
                      <div className='mt-2'>
                        <PrioritySelector
                          priority={
                            priorities.find(
                              (p) => p.id === form.watch('priority_id')
                            )
                              ? {
                                  id: form.watch('priority_id') || '',
                                  name:
                                    priorities.find(
                                      (p) => p.id === form.watch('priority_id')
                                    )?.name || '',
                                  icon_name:
                                    priorities.find(
                                      (p) => p.id === form.watch('priority_id')
                                    )?.icon_name || '',
                                  sort_order:
                                    priorities.find(
                                      (p) => p.id === form.watch('priority_id')
                                    )?.sort_order || 0,
                                }
                              : null
                          }
                          onPriorityChange={(priorityId) =>
                            form.setValue('priority_id', priorityId)
                          }
                        />
                      </div>
                    </div>

                    <div>
                      <Label>Health Status</Label>
                      <div className='mt-2'>
                        <HealthPopover
                          project={{
                            id: 'temp',
                            name: form.watch('name') || 'New Project',
                            health: {
                              id: form.watch('health_id') || 'on-track',
                              name:
                                form.watch('health_id') === 'on-track'
                                  ? 'On Track'
                                  : form.watch('health_id') === 'off-track'
                                    ? 'Off Track'
                                    : form.watch('health_id') === 'at-risk'
                                      ? 'At Risk'
                                      : 'No Update',
                              description:
                                form.watch('health_id') === 'on-track'
                                  ? 'Project is progressing as planned'
                                  : form.watch('health_id') === 'off-track'
                                    ? 'Project is behind schedule or facing issues'
                                    : form.watch('health_id') === 'at-risk'
                                      ? 'Project may face delays or issues'
                                      : 'No recent status update available',
                            },
                          }}
                          onHealthChange={(healthId) =>
                            form.setValue(
                              'health_id',
                              healthId as
                                | 'no-update'
                                | 'off-track'
                                | 'on-track'
                                | 'at-risk'
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Timeline</CardTitle>
                </CardHeader>
                <CardContent className='space-y-6'>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                      <Label>Start Date</Label>
                      <div className='mt-2'>
                        <DatePicker
                          date={
                            form.watch('start_date')
                              ? new Date(form.watch('start_date') || '')
                              : undefined
                          }
                          onDateChange={(date) =>
                            form.setValue(
                              'start_date',
                              date ? date.toISOString().split('T')[0] : ''
                            )
                          }
                        />
                      </div>
                    </div>

                    <div>
                      <Label>Target Date</Label>
                      <div className='mt-2'>
                        <DatePicker
                          date={
                            form.watch('target_date')
                              ? new Date(form.watch('target_date') || '')
                              : undefined
                          }
                          onDateChange={(date) =>
                            form.setValue(
                              'target_date',
                              date ? date.toISOString().split('T')[0] : ''
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Affiliate Information</CardTitle>
                  <CardDescription>
                    Optional information if this project was proposed by an
                    affiliate
                  </CardDescription>
                </CardHeader>
                <CardContent className='space-y-6'>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                      <FormField
                        control={form.control}
                        name='is_proposed'
                        render={({ field }) => (
                          <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                            <div className='space-y-0.5'>
                              <FormLabel className='text-base'>
                                Affiliate Proposal
                              </FormLabel>
                              <FormDescription>
                                This project was proposed by an affiliate
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={isSubmitting}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    {form.watch('is_proposed') && (
                      <div>
                        <Label>Affiliate User</Label>
                        <div className='mt-2'>
                          <AffiliateSelector
                            affiliate={
                              members.find(
                                (m) =>
                                  m.id === form.watch('affiliate_user') &&
                                  m.role === 'Affiliate'
                              )
                                ? {
                                    id: form.watch('affiliate_user') || '',
                                    name:
                                      members.find(
                                        (m) =>
                                          m.id === form.watch('affiliate_user')
                                      )?.full_name ||
                                      members.find(
                                        (m) =>
                                          m.id === form.watch('affiliate_user')
                                      )?.name ||
                                      '',
                                    email:
                                      members.find(
                                        (m) =>
                                          m.id === form.watch('affiliate_user')
                                      )?.email || '',
                                    avatar_url:
                                      members.find(
                                        (m) =>
                                          m.id === form.watch('affiliate_user')
                                      )?.avatar_url || null,
                                  }
                                : null
                            }
                            onAffiliateChange={(affiliateId) =>
                              form.setValue('affiliate_user', affiliateId)
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Team Members</CardTitle>
                </CardHeader>
                <CardContent>
                  <ProjectMembersSelector
                    selectedMembers={selectedMembers}
                    onMembersChange={setSelectedMembers}
                    disabled={isSubmitting}
                  />
                </CardContent>
              </Card>
            </form>
          </Form>
        </div>
      </section>
    </main>
  );
}
