'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Shield, FileText, CheckCircle, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { usePortfolio } from '@/hooks/use-db';

export default function NDAPage() {
  const router = useRouter();
  const { portfolioData, loading, signNDA } = usePortfolio();
  const [hasRead, setHasRead] = useState(false);
  const [isAgreeing, setIsAgreeing] = useState(false);

  const handleSignNDA = () => {
    if (!hasRead) {
      toast.error('Please confirm that you have read and understood the NDA');
      return;
    }

    setIsAgreeing(true);

    signNDA()
      .then(() => {
        toast.success('NDA signed successfully!');
        router.push('/affiliate');
      })
      .catch((error) => {
        toast.error(`Failed to sign NDA: ${error.message}`);
      })
      .finally(() => {
        setIsAgreeing(false);
      });
  };

  if (loading) {
    return (
      <main className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </main>
    );
  }

  // If NDA is already signed, show confirmation
  if (portfolioData?.is_nda_signed) {
    return (
      <main className="h-full flex items-center justify-center p-6">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <CardTitle>NDA Already Signed</CardTitle>
            <CardDescription>
              You have already signed the Non-Disclosure Agreement
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-muted-foreground mb-4">
              Signed on: {portfolioData.nda_signed_date 
                ? new Date(portfolioData.nda_signed_date).toLocaleDateString()
                : 'Previously signed'
              }
            </p>
            <Button onClick={() => router.push('/affiliate')} className="w-full">
              Continue to Dashboard
            </Button>
          </CardContent>
        </Card>
      </main>
    );
  }

  return (
    <main className="h-full flex flex-col">
      <header className="pl-12 pr-6 h-11 inline-flex items-center border-b border-neutral-300 dark:border-neutral-800 w-full">
        <div className="flex items-center space-x-2">
          <Shield className="h-4 w-4" />
          <p className="font-medium text-sm">Non-Disclosure Agreement</p>
        </div>
      </header>
      
      <section className="flex-1 p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Warning Notice */}
          <Card className="border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                <CardTitle className="text-yellow-800 dark:text-yellow-200">
                  Required Agreement
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-yellow-800 dark:text-yellow-200">
                You must read and agree to this Non-Disclosure Agreement before accessing 
                your affiliate dashboard and tools.
              </p>
            </CardContent>
          </Card>

          {/* NDA Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Non-Disclosure Agreement</span>
              </CardTitle>
              <CardDescription>
                Please read the following agreement carefully
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96 w-full border rounded-lg p-4">
                <div className="space-y-4 text-sm">
                  <h3 className="font-semibold text-base">NON-DISCLOSURE AGREEMENT</h3>
                  
                  <p>
                    This Non-Disclosure Agreement ("Agreement") is entered into between 
                    The Hue Factory ("Company") and the undersigned affiliate ("Affiliate").
                  </p>

                  <h4 className="font-semibold">1. CONFIDENTIAL INFORMATION</h4>
                  <p>
                    Confidential Information includes, but is not limited to: client information, 
                    business strategies, financial data, proprietary processes, marketing plans, 
                    customer lists, pricing information, and any other non-public information 
                    disclosed by the Company.
                  </p>

                  <h4 className="font-semibold">2. OBLIGATIONS</h4>
                  <p>
                    The Affiliate agrees to:
                  </p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li>Keep all Confidential Information strictly confidential</li>
                    <li>Not disclose Confidential Information to any third party</li>
                    <li>Use Confidential Information solely for authorized affiliate activities</li>
                    <li>Return or destroy all Confidential Information upon request</li>
                  </ul>

                  <h4 className="font-semibold">3. TERM</h4>
                  <p>
                    This Agreement remains in effect during the affiliate relationship and 
                    continues for a period of five (5) years after termination.
                  </p>

                  <h4 className="font-semibold">4. REMEDIES</h4>
                  <p>
                    The Affiliate acknowledges that breach of this Agreement may cause 
                    irreparable harm to the Company, and the Company may seek injunctive 
                    relief and monetary damages.
                  </p>

                  <h4 className="font-semibold">5. GOVERNING LAW</h4>
                  <p>
                    This Agreement is governed by the laws of the jurisdiction where 
                    The Hue Factory is incorporated.
                  </p>

                  <p className="font-semibold mt-6">
                    By signing this agreement, you acknowledge that you have read, 
                    understood, and agree to be bound by all terms and conditions.
                  </p>
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Agreement Section */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="nda-agreement"
                    checked={hasRead}
                    onCheckedChange={(checked) => setHasRead(checked as boolean)}
                  />
                  <label
                    htmlFor="nda-agreement"
                    className="text-sm leading-relaxed cursor-pointer"
                  >
                    I have read and understood the Non-Disclosure Agreement above, 
                    and I agree to be bound by all its terms and conditions.
                  </label>
                </div>

                <div className="flex justify-end space-x-4">
                  <Button
                    variant="outline"
                    onClick={() => router.push('/auth/logout')}
                    disabled={isAgreeing}
                  >
                    Decline & Logout
                  </Button>
                  <Button
                    onClick={handleSignNDA}
                    disabled={!hasRead || isAgreeing}
                  >
                    {isAgreeing ? 'Signing...' : 'Sign Agreement'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </main>
  );
}
