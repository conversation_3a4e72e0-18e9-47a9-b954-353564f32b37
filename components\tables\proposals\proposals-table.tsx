'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import { ViewProposalDialog } from '@/components/proposals/view-proposal-dialog';
import { DataTable } from '@/components/ui/data-table';
import { useProposals } from '@/hooks/use-db';
import type { Proposal } from '@/lib/supabase/database-modules';

import { createColumns } from './columns';

export function ProposalsTable() {
  const { proposals, loading, error, updateProposal } = useProposals();
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(
    null
  );
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  const handleViewProposal = (proposal: Proposal) => {
    setSelectedProposal(proposal);
    setIsViewDialogOpen(true);
  };

  const handleApprovalChange = (
    proposalId: number,
    approved: boolean | null
  ) => {
    const updatePromise = updateProposal(proposalId, { is_approved: approved });

    const statusText =
      approved === true
        ? 'approved'
        : approved === false
          ? 'rejected'
          : 'pending';

    toast.promise(updatePromise, {
      loading: 'Updating proposal status...',
      success: `Proposal ${statusText} successfully`,
      error: (error) =>
        `Failed to update proposal: ${error?.message || 'Unknown error'}`,
    });
  };

  const columns = createColumns({
    onViewProposal: handleViewProposal,
    onApprovalChange: handleApprovalChange,
  });

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-destructive'>Error: {error}</div>
      </div>
    );
  }

  return (
    <>
      <DataTable columns={columns} data={proposals} isLoading={loading} />

      <ViewProposalDialog
        proposal={selectedProposal}
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
      />
    </>
  );
}
