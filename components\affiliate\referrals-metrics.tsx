'use client';

import { useMemo } from 'react';
import {
  TrendingDown,
  TrendingUp,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { Proposal } from '@/lib/supabase/database-modules';

interface ReferralsMetricsProps {
  proposals: Proposal[];
  loading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  trend?: 'up' | 'down' | 'neutral';
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  icon?: React.ReactNode;
}

function MetricCard({
  title,
  value,
  subtitle,
  trend = 'neutral',
  variant = 'default',
  icon,
}: MetricCardProps) {
  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : null;

  const cardVariants = {
    default: 'border-border',
    success: 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950',
    warning: 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950',
    destructive: 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950',
  };

  const iconVariants = {
    default: 'text-muted-foreground',
    success: 'text-green-600 dark:text-green-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    destructive: 'text-red-600 dark:text-red-400',
  };

  const trendVariants = {
    up: 'text-green-600 dark:text-green-400',
    down: 'text-red-600 dark:text-red-400',
    neutral: 'text-muted-foreground',
  };

  return (
    <Card className={cardVariants[variant]}>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium'>{title}</CardTitle>
        {icon && <div className={iconVariants[variant]}>{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className='text-2xl font-bold'>{value}</div>
        <div className='flex items-center text-xs text-muted-foreground'>
          {TrendIcon && <TrendIcon className={`mr-1 h-3 w-3 ${trendVariants[trend]}`} />}
          <span>{subtitle}</span>
        </div>
      </CardContent>
    </Card>
  );
}

export function ReferralsMetrics({
  proposals,
  loading,
}: ReferralsMetricsProps) {
  const metrics = useMemo(() => {
    if (!proposals.length) {
      return {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        newThisWeek: 0,
      };
    }

    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const total = proposals.length;
    const pending = proposals.filter(
      (proposal) => proposal.is_approved === null
    ).length;
    const approved = proposals.filter(
      (proposal) => proposal.is_approved === true
    ).length;
    const rejected = proposals.filter(
      (proposal) => proposal.is_approved === false
    ).length;
    const newThisWeek = proposals.filter((proposal) => {
      return new Date(proposal.created_at) >= lastWeek;
    }).length;

    return {
      total,
      pending,
      approved,
      rejected,
      newThisWeek,
    };
  }, [proposals]);

  const approvalRate = metrics.total > 0 
    ? Math.round((metrics.approved / metrics.total) * 100) 
    : 0;

  if (loading) {
    return (
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {['Total Referrals', 'Pending Review', 'Approved', 'Rejected'].map((title) => (
          <Card key={title}>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>{title}</CardTitle>
              <div className='h-4 w-4 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse' />
            </CardHeader>
            <CardContent>
              <div className='h-8 w-16 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse mb-2' />
              <div className='h-4 w-24 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse' />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
      <MetricCard
        title='Total Referrals'
        value={metrics.total}
        subtitle={`${metrics.newThisWeek} new this week`}
        trend={metrics.newThisWeek > 0 ? 'up' : 'neutral'}
        icon={<FileText className='h-4 w-4' />}
      />

      <MetricCard
        title='Pending Review'
        value={metrics.pending}
        subtitle={`${Math.round((metrics.pending / (metrics.total || 1)) * 100)}% of total`}
        variant={metrics.pending > 0 ? 'warning' : 'default'}
        icon={<Clock className='h-4 w-4' />}
      />

      <MetricCard
        title='Approved'
        value={metrics.approved}
        subtitle={`${approvalRate}% approval rate`}
        variant={metrics.approved > 0 ? 'success' : 'default'}
        trend={approvalRate > 50 ? 'up' : approvalRate < 30 ? 'down' : 'neutral'}
        icon={<CheckCircle className='h-4 w-4' />}
      />

      <MetricCard
        title='Rejected'
        value={metrics.rejected}
        subtitle={`${Math.round((metrics.rejected / (metrics.total || 1)) * 100)}% of total`}
        variant={metrics.rejected > 0 ? 'destructive' : 'default'}
        icon={<XCircle className='h-4 w-4' />}
      />
    </div>
  );
}
