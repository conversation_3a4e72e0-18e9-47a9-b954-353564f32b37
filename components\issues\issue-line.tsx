'use client';

import { format } from 'date-fns';
import { motion } from 'motion/react';
import { memo } from 'react';

import { ContextMenu, ContextMenuTrigger } from '@/components/ui/context-menu';
import type { Issue } from '@/lib/supabase/database-modules';

import { AssigneeUser } from './assignee-user';
import { IssueContextMenu } from './issue-context-menu';
import { LabelBadge } from './label-badge';
import { PrioritySelector } from './priority-selector';
import { ProjectBadge } from './project-badge';
import { StatusSelector } from './status-selector';

interface PriorityData {
  id: string;
  name: string;
  icon_name?: string | null;
  sort_order?: number | null;
}

interface StatusData {
  id: string;
  name: string;
  color: string;
  sort_order?: number | null;
}

interface UserData {
  id: string;
  full_name?: string | null;
  name?: string;
  email: string;
  avatar_url?: string | null;
}

interface LabelData {
  id: string;
  name: string;
  color: string;
}

// Helper functions to convert database types to component types
const convertPriority = (priority: PriorityData | null | undefined) => {
  if (!priority) return null;
  return {
    id: priority.id,
    name: priority.name,
    icon_name: priority.icon_name || '',
    sort_order: priority.sort_order || 0,
  };
};

const convertStatus = (status: StatusData | null | undefined) => {
  if (!status) return null;
  return {
    id: status.id,
    name: status.name,
    color: status.color,
    sort_order: status.sort_order || 0,
  };
};

const convertUser = (user: UserData | null | undefined) => {
  if (!user) return null;
  return {
    id: user.id,
    name: user.full_name || user.name || user.email,
    email: user.email,
    avatar_url: user.avatar_url,
  };
};

const convertLabels = (labels: LabelData[] | null | undefined) => {
  if (!labels) return [];
  return labels.map((label) => ({
    id: label.id,
    name: label.name,
    color: label.color,
  }));
};

export const IssueLine = memo(function IssueLine({
  issue,
  layoutId = false,
}: {
  issue: Issue;
  layoutId?: boolean;
}) {
  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        <motion.div
          {...(layoutId && { layoutId: `issue-line-${issue.identifier}` })}
          // TODO: Add navigation to issue detail page
          // href={`/issues/${issue.identifier}`}
          className='w-full flex items-center justify-start h-11 px-6 hover:bg-sidebar/50'
        >
          <div className='flex items-center gap-0.5'>
            <PrioritySelector
              priority={convertPriority(issue.priority)}
              issueId={issue.id}
            />
            {/* <span className='text-sm hidden sm:inline-block text-muted-foreground font-medium w-[66px] truncate shrink-0 mr-0.5'>
              {issue.identifier}
            </span> */}
            <StatusSelector
              status={convertStatus(issue.status)}
              issueId={issue.id}
            />
          </div>
          <span className='min-w-0 flex items-center justify-start mr-1 ml-0.5'>
            <span className='text-xs sm:text-sm font-medium sm:font-semibold truncate'>
              {issue.title}
            </span>
          </span>
          <div className='flex items-center justify-end gap-2 ml-auto sm:w-fit'>
            <div className='w-3 shrink-0'></div>
            <div className='-space-x-5 hover:space-x-1 lg:space-x-1 items-center justify-end hidden sm:flex duration-200 transition-all'>
              <LabelBadge labels={convertLabels(issue.labels || [])} />
              {issue.project && <ProjectBadge project={issue.project} />}
            </div>
            <span className='text-xs text-muted-foreground shrink-0 hidden sm:inline-block'>
              {(() => {
                const date = new Date(issue.created_at);
                return Number.isNaN(date.getTime())
                  ? 'Invalid'
                  : format(date, 'MMM dd');
              })()}
            </span>
            <AssigneeUser user={convertUser(issue.assignee)} />
          </div>
        </motion.div>
      </ContextMenuTrigger>
      <IssueContextMenu issueId={issue.id} />
    </ContextMenu>
  );
});
