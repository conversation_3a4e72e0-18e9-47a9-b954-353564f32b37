'use client';

import { useState } from 'react';
import { Check, ChevronDown } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Task } from '@/lib/supabase/database-modules';

interface TaskStatusSelectorProps {
  status: Task['status'];
  onStatusChange?: (status: Task['status']) => void;
}

const statusOptions: { value: Task['status']; label: string; color: string }[] = [
  { value: 'backlog', label: 'Backlog', color: 'bg-gray-100 text-gray-800' },
  { value: 'todo', label: 'To Do', color: 'bg-blue-100 text-blue-800' },
  { value: 'in_progress', label: 'In Progress', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'review', label: 'In Review', color: 'bg-purple-100 text-purple-800' },
  { value: 'completed', label: 'Completed', color: 'bg-green-100 text-green-800' },
];

export function TaskStatusSelector({ status, onStatusChange }: TaskStatusSelectorProps) {
  const [open, setOpen] = useState(false);

  const currentStatus = statusOptions.find((option) => option.value === status);

  const handleStatusSelect = (newStatus: Task['status']) => {
    if (onStatusChange) {
      onStatusChange(newStatus);
    }
    setOpen(false);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className='h-auto p-1 justify-start'
        >
          <Badge className={currentStatus?.color}>
            {currentStatus?.label}
          </Badge>
          <ChevronDown className='ml-1 h-3 w-3' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start' className='w-40'>
        {statusOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleStatusSelect(option.value)}
            className='flex items-center justify-between'
          >
            <Badge className={option.color}>
              {option.label}
            </Badge>
            {status === option.value && (
              <Check className='h-4 w-4' />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
