'use client';

import { useEffect, useId, useState } from 'react';
import { CheckIcon } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { supabaseClient } from '@/lib/supabase/auth/client';

interface UserOption {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
}

interface AffiliateSelectorProps {
  affiliate: UserOption | null;
  onAffiliateChange?: (userId: string) => void;
}

export function AffiliateSelector({ affiliate, onAffiliateChange }: AffiliateSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>(affiliate?.id || '');
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setValue(affiliate?.id || '');
  }, [affiliate?.id]);

  // Load user options from Supabase profiles table (affiliates only)
  useEffect(() => {
    const loadUserOptions = async () => {
      try {
        setLoading(true);

        const { data, error } = await supabaseClient
          .from('profiles')
          .select('id, full_name, email, avatar_url')
          .eq('role', 'Affiliate')
          .order('full_name');

        if (error) {
          console.error('Failed to load affiliate options:', error);
        } else {
          const users =
            data?.map((profile) => ({
              id: profile.id,
              name: profile.full_name || profile.email,
              email: profile.email,
              avatar_url: profile.avatar_url,
            })) || [];
          setUserOptions(users);
        }
      } catch (error) {
        console.error('Failed to load affiliate options:', error);
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      loadUserOptions();
    }
  }, [open]);

  const handleAffiliateChange = (userId: string) => {
    setValue(userId);
    setOpen(false);

    if (onAffiliateChange) {
      onAffiliateChange(userId);
    }
  };

  return (
    <div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='flex items-center justify-center gap-1 h-7 px-2'
            size='sm'
            variant='ghost'
            // role='combobox'
            aria-expanded={open}
          >
            {affiliate ? (
              <>
                <Avatar className='size-5 mr-1'>
                  <AvatarImage
                    src={affiliate.avatar_url || undefined}
                    alt={affiliate.name}
                  />
                  <AvatarFallback>{affiliate.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <span className='text-xs hidden md:inline'>{affiliate.name}</span>
              </>
            ) : (
              <span className='text-xs'>Select affiliate</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className='border-input w-48 p-0' align='start'>
          <Command>
            <CommandInput placeholder='Set affiliate...' />
            <CommandList>
              <CommandEmpty>No affiliate found.</CommandEmpty>
              <CommandGroup>
                {loading ? (
                  <CommandItem disabled>
                    <span>Loading affiliates...</span>
                  </CommandItem>
                ) : (
                  userOptions.map((user) => (
                    <CommandItem
                      key={user.id}
                      value={user.id}
                      onSelect={handleAffiliateChange}
                      className='flex items-center justify-between'
                    >
                      <div className='flex items-center gap-2'>
                        <Avatar className='size-5'>
                          <AvatarImage
                            src={user.avatar_url || undefined}
                            alt={user.name}
                          />
                          <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <span className='text-xs'>{user.name}</span>
                      </div>
                      {(value === user.id || affiliate?.id === user.id) && (
                        <CheckIcon size={14} className='ml-auto' />
                      )}
                    </CommandItem>
                  ))
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
