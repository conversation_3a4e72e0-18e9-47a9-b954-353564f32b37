'use client';

import type * as React from 'react';

import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>barFooter,
  SidebarHeader,
} from '@/components/ui/sidebar';
import { useProfile } from '@/hooks/use-db';
import type {
  Profile_Types,
  SidebarData,
} from '@/lib/supabase/database-modules';
import adminSidebarData, { nullSidebarData } from './admin/content';
import affiliateSidebarData from './affiliates/content';
import collaboratorsSidebarData from './collaborators/content';
import { NavFooter } from './nav-footer';
import { NavHeader } from './nav-header';
import { NavMain } from './nav-main';
import { NavUser } from './nav-user';

// Main function to generate sidebar data based on user role
export const getSidebarData = (profile: Profile_Types): SidebarData => {
  if (profile.role === 'Admin') {
    return adminSidebarData({ profile });
  } else if (profile.role === 'Affiliate') {
    return affiliateSidebarData({ profile });
  } else if (profile.role === 'Collaborator') {
    return collaboratorsSidebarData({ profile });
  } else {
    return nullSidebarData({ profile });
  }
};

// Updated AppSidebar component
interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  // Profile: Profile_Types;
}

export function AppSidebar({ ...props }: AppSidebarProps) {
  const { profile } = useProfile();

  if (!profile) {
    // Sidebar skeleton while loading profile
    return (
      <Sidebar collapsible='offcanvas' {...props}>
        <SidebarHeader>
          <div className='flex items-center space-x-3 py-4 px-6'>
            <div className='w-10 h-10 rounded-full bg-neutral-200 dark:bg-neutral-800 animate-pulse' />
            <div className='flex-1'>
              <div className='h-4 w-24 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse mb-2' />
              <div className='h-3 w-16 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse' />
            </div>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <div className='space-y-4 px-4 py-2'>
            {[1, 2, 3].map((num) => (
              <div
                key={`sidebar-skeleton-${num}`}
                className='h-4 w-32 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse mb-2'
              />
            ))}
            <div className='h-3 w-20 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse mb-4' />
            {[1, 2, 3, 4, 5].map((num) => (
              <div
                key={`sidebar-skeleton-main-${num}`}
                className='h-4 w-28 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse mb-2'
              />
            ))}
          </div>
        </SidebarContent>
        <SidebarFooter>
          <div className='h-8 w-24 bg-neutral-200 dark:bg-neutral-800 rounded mx-4 my-4 animate-pulse' />
        </SidebarFooter>
      </Sidebar>
    );
  }

  const sidebarData = getSidebarData(profile);

  return (
    <Sidebar collapsible='offcanvas' {...props}>
      <SidebarHeader>
        <NavUser user={sidebarData.user} />
      </SidebarHeader>
      <SidebarContent>
        <NavHeader items={sidebarData.navHeader} />
        <NavMain items={sidebarData.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavFooter />
      </SidebarFooter>
    </Sidebar>
  );
}
