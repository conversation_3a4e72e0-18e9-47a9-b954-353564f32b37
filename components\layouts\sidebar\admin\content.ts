import {
  Archive,
  BookUser,
  Box,
  Briefcase,
  DollarSign,
  FileText,
  Heart,
  Home,
  Inbox,
  Layers,
  Mail,
  PenTool,
  Shield,
  Tag,
  Users,
  UsersRound,
  Zap,
} from 'lucide-react';
import type { SidebarData } from '@/lib/supabase/database-modules';
import type { Database } from '@/lib/supabase/database-types';

type Profile_Types = Database['public']['Tables']['profiles']['Row'];

const adminSidebarData = ({
  profile,
}: {
  profile: Profile_Types;
}): SidebarData => {
  const user = {
    name: profile.full_name,
    email: profile.email,
    avatar: '/avatars/admin.jpg',
    role: profile.role,
  };
  const teams = [
    {
      name: 'Admin Panel',
      logo: Shield,
      plan: 'Enterprise',
    },
  ];
  const navHeader = [
    {
      title: 'Home',
      url: '/admin',
      icon: Home,
    },
    {
      title: 'Inbox',
      url: '/admin/inbox',
      icon: Inbox,
    },
  ];
  const navMain = [
    {
      title: 'workspace',
      items: [
        {
          title: 'Projects',
          url: '/admin/projects',
          icon: Box,
          isActive: true,
        },
        {
          title: 'Issues',
          url: '/admin/issues',
          icon: Layers,
          isActive: true,
        },
        {
          title: 'Teams',
          url: '/admin/teams',
          icon: UsersRound,
          isActive: true,
        },
        {
          title: 'Members',
          url: '/admin/members',
          icon: Users,
          isActive: true,
          items: [
            {
              title: 'Staff',
              url: '/admin/members/staff',
              icon: Tag,
            },
            {
              title: 'Interns',
              url: '/admin/members/interns',
              icon: Heart,
            },
            {
              title: 'Collaborators',
              url: '/admin/members/collaborators',
              icon: PenTool,
            },
            {
              title: 'Affiliates',
              url: '/admin/members/affiliates',
              icon: Briefcase,
            },
          ],
        },
        {
          title: 'Applications',
          url: '/admin/applications',
          icon: BookUser,
          isActive: true,
        },
        {
          title: 'Database',
          url: '/admin/database',
          icon: Mail,
          isActive: true,
        },
        {
          title: 'Client Requests',
          url: '/admin/client-requests',
          icon: Archive,
          isActive: true,
        },
      ],
    },
    {
      title: 'Finance',
      items: [
        {
          title: 'Revenue',
          url: '/admin/revenue',
          icon: Zap,
          isActive: true,
        },
        {
          title: 'Payouts',
          url: '/admin/payouts',
          icon: DollarSign,
          isActive: true,
        },
        {
          title: 'Invoicing',
          url: '/admin/invoicing',
          icon: FileText,
          isActive: true,
        },
      ],
    },
  ];

  return {
    user,
    teams,
    navMain,
    navHeader,
  };
};
export const nullSidebarData = ({
  profile,
}: {
  profile: Profile_Types;
}): SidebarData => {
  const user = {
    name: profile.full_name,
    email: profile.email,
    avatar: '/avatars/admin.jpg',
    role: profile.role,
  };
  const teams = [
    {
      name: 'Admin Panel',
      logo: Shield,
      plan: 'Enterprise',
    },
  ];
  const navHeader = [
    {
      title: 'Home',
      url: '/',
      icon: Home,
    },
  ];
  const navMain = [
    {
      title: 'workspace',
      items: [
        {
          title: 'Projects',
          url: '/admin/projects',
          icon: Box,
          isActive: true,
        },
      ],
    },
  ];

  return {
    user,
    teams,
    navMain,
    navHeader,
  };
};

export default adminSidebarData;
