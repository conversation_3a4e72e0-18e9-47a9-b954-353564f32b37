'use client';

import { CheckIcon } from 'lucide-react';
import { useId, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useStatus } from '@/hooks/use-db';
import { StatusIcon } from '@/lib/constants/status';

interface StatusSelectorProps {
  value: string;
  onChange: (statusId: string) => void;
}

export function StatusSelector({ value, onChange }: StatusSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const { status: statusOptions, loading } = useStatus();

  const handleStatusChange = (statusId: string) => {
    setOpen(false);
    onChange(statusId);
  };

  return (
    <div className='*:not-first:mt-2'>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='flex items-center justify-center gap-2'
            size='sm'
            variant='secondary'
            // role='combobox'
            aria-expanded={open}
            disabled={loading}
          >
            {(() => {
              const selectedItem = statusOptions.find(
                (item) => item.id === value
              );
              if (selectedItem) {
                return <StatusIcon statusId={selectedItem.id} />;
              }
              return null;
            })()}
            <span>
              {loading
                ? 'Loading...'
                : value
                  ? statusOptions.find((s) => s.id === value)?.name
                  : 'Select status'}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
          align='start'
        >
          <Command>
            <CommandInput placeholder='Set status...' />
            <CommandList>
              <CommandEmpty>No status found.</CommandEmpty>
              <CommandGroup>
                {statusOptions.map((item) => (
                  <CommandItem
                    key={item.id}
                    value={item.id}
                    onSelect={() => handleStatusChange(item.id)}
                    className='flex items-center justify-between'
                  >
                    <div className='flex items-center gap-2'>
                      {/* <div
                        className='w-3 h-3 rounded-full'
                        style={{ backgroundColor: item.color }}
                      /> */}
                      <StatusIcon statusId={item.id} />
                      {item.name}
                    </div>
                    {value === item.id && (
                      <CheckIcon size={16} className='ml-auto' />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
