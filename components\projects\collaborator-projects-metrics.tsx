'use client';

import { useMemo } from 'react';
import {
  TrendingDown,
  TrendingUp,
  Folder<PERSON>pen,
  Target,
  AlertTriangle,
  CheckCircle,
  Users,
  Clock,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { Project } from '@/lib/supabase/database-modules';

interface CollaboratorProjectsMetricsProps {
  projects: Project[];
  loading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  trend?: 'up' | 'down' | 'neutral';
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  icon?: React.ReactNode;
}

function MetricCard({
  title,
  value,
  subtitle,
  trend,
  variant = 'default',
  icon,
}: MetricCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'destructive':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      default:
        return 'border-border bg-card';
    }
  };

  const getTrendIcon = () => {
    if (trend === 'up')
      return <TrendingUp className='h-4 w-4 text-green-600' />;
    if (trend === 'down')
      return <TrendingDown className='h-4 w-4 text-red-600' />;
    return null;
  };

  return (
    <Card className={`${getVariantStyles()} transition-all`}>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium text-muted-foreground'>
          {title}
        </CardTitle>
        {icon && <div className='text-muted-foreground'>{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className='flex items-center justify-between'>
          <div>
            <div className='text-2xl font-bold'>{value}</div>
            <div className='flex items-center gap-1 text-xs text-muted-foreground'>
              {getTrendIcon()}
              <span>{subtitle}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function CollaboratorProjectsMetrics({ 
  projects, 
  loading 
}: CollaboratorProjectsMetricsProps) {
  const metrics = useMemo(() => {
    if (!projects.length) {
      return {
        total: 0,
        active: 0,
        completed: 0,
        leading: 0,
        participating: 0,
        overdue: 0,
        averageCompletion: 0,
      };
    }

    const now = new Date();
    
    const total = projects.length;
    const active = projects.filter(
      (project) => !project.is_archived && project.percent_complete < 100
    ).length;
    const completed = projects.filter(
      (project) => project.percent_complete === 100
    ).length;
    
    // Count projects where user is the lead vs participating as member
    const leading = projects.filter(
      (project) => project.lead_id // This will be filtered by the hook to current user
    ).length;
    const participating = total - leading;

    // Count overdue projects (past target date and not completed)
    const overdue = projects.filter((project) => {
      if (!project.target_date || project.percent_complete === 100) return false;
      return new Date(project.target_date) < now;
    }).length;

    const totalCompletion = projects.reduce(
      (sum, project) => sum + project.percent_complete,
      0
    );
    const averageCompletion =
      total > 0 ? Math.round(totalCompletion / total) : 0;

    return {
      total,
      active,
      completed,
      leading,
      participating,
      overdue,
      averageCompletion,
    };
  }, [projects]);

  if (loading) {
    return (
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {['total', 'active', 'leading', 'overdue'].map((metric) => (
          <Card key={metric} className='animate-pulse'>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <div className='h-4 w-20 bg-muted'></div>
              <div className='h-4 w-4 bg-muted'></div>
            </CardHeader>
            <CardContent>
              <div className='h-8 w-16 bg-muted mb-2'></div>
              <div className='h-3 w-24 bg-muted'></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const completionRate =
    metrics.total > 0
      ? Math.round((metrics.completed / metrics.total) * 100)
      : 0;
  const overduePercentage =
    metrics.active > 0 ? Math.round((metrics.overdue / metrics.active) * 100) : 0;

  return (
    <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
      <MetricCard
        title='My Projects'
        value={metrics.total}
        subtitle={`${metrics.averageCompletion}% avg completion`}
        trend={metrics.averageCompletion > 70 ? 'up' : metrics.averageCompletion < 30 ? 'down' : 'neutral'}
        icon={<FolderOpen className='h-4 w-4' />}
      />

      <MetricCard
        title='Active Projects'
        value={metrics.active}
        subtitle={`${metrics.leading} leading, ${metrics.participating} participating`}
        variant={metrics.active > 5 ? 'warning' : 'default'}
        icon={<Target className='h-4 w-4' />}
      />

      <MetricCard
        title='Completed'
        value={metrics.completed}
        subtitle={`${completionRate}% completion rate`}
        variant='success'
        trend={
          completionRate > 70 ? 'up' : completionRate < 30 ? 'down' : 'neutral'
        }
        icon={<CheckCircle className='h-4 w-4' />}
      />

      <MetricCard
        title='Overdue'
        value={metrics.overdue}
        subtitle={`${overduePercentage}% of active projects`}
        variant={metrics.overdue > 0 ? 'destructive' : 'success'}
        icon={<Clock className='h-4 w-4' />}
      />
    </div>
  );
}
