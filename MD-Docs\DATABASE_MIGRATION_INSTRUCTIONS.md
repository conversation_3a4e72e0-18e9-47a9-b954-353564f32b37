# Database Migration Instructions

## Overview
Since the Supabase MCP server is in read-only mode, you'll need to manually run the database migration script to set up the issues management system.

## Steps to Execute

### 1. Open Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor

### 2. Run the Migration Script
1. Open the file `database_migration_issues_schema.sql` in this directory
2. Copy the entire contents of the file
3. Paste it into the Supabase SQL Editor
4. Click "Run" to execute the migration

### 3. What the Migration Does

#### Database Schema Changes:
- **Updates profiles table**: Adds missing columns (name, status, joined_date, created_at) to match the expected schema
- **Creates reference tables**: status, priorities, labels
- **Creates main entity tables**: teams, team_members, cycles
- **Updates projects table**: Adds missing columns to work with the issues system
- **Creates issues table**: The main table for issue management
- **Creates junction tables**: issue_labels, project_teams

#### Security & Performance:
- **Enables Row Level Security (RLS)** on all tables
- **Creates security policies** for proper access control
- **Adds database indexes** for optimal query performance
- **Creates triggers** for automatic timestamp updates

#### Sample Data:
- **Status options**: In Progress, Technical Review, Completed, Paused, Todo, Backlog
- **Priority levels**: No priority, Urgent, High, Medium, Low
- **Label categories**: UI, Bug, Feature, Documentation, etc.

### 4. Verification
After running the migration, the script will show a verification query result showing all created tables with ✅ status.

### 5. Expected Tables After Migration
- `profiles` (updated)
- `status` (new)
- `priorities` (new)
- `labels` (new)
- `teams` (new)
- `team_members` (new)
- `projects` (updated)
- `cycles` (new)
- `issues` (new)
- `issue_labels` (new)
- `project_teams` (new)

## Next Steps After Migration
Once the database migration is complete, we can proceed with:
1. **Phase 2**: Hook Integration - Integrating the useIssues hook
2. **Phase 3**: API Layer Implementation
3. **Phase 4**: Real-time Features
4. **Phase 5**: Component Integration

## Troubleshooting
If you encounter any errors during migration:
1. Check the error message in the Supabase SQL Editor
2. Ensure you have the necessary permissions
3. The script uses `IF NOT EXISTS` and `ON CONFLICT` clauses to prevent errors on re-runs
4. You can safely re-run the script if needed

## Important Notes
- The migration is designed to be **non-destructive** - it won't delete existing data
- It uses conditional logic to only add missing columns/tables
- All changes are backward compatible with your existing data
- The script can be run multiple times safely