'use client';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Eye, MoreHorizontal } from 'lucide-react';

import { ApprovalStatusSelector } from '@/components/proposals/approval-status-selector';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Proposal } from '@/lib/supabase/database-modules';

interface ColumnsProps {
  onViewProposal: (proposal: Proposal) => void;
  onApprovalChange: (proposalId: number, approved: boolean | null) => void;
}

const getClientName = (proposal: Proposal): string => {
  if (proposal.affiliate_proposal?.client_name) {
    return proposal.affiliate_proposal.client_name;
  }
  return 'Unknown Client';
};

export const createColumns = ({
  onViewProposal,
  onApprovalChange,
}: ColumnsProps): ColumnDef<Proposal>[] => [
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => {
      const createdAt = row.getValue('created_at') as string;

      if (!createdAt) {
        return <div className='font-medium text-muted-foreground'>Unknown</div>;
      }

      const date = new Date(createdAt);
      if (Number.isNaN(date.getTime())) {
        return (
          <div className='font-medium text-muted-foreground'>Invalid Date</div>
        );
      }

      return (
        <div className='font-medium'>{format(date, 'MMM dd, yyyy HH:mm')}</div>
      );
    },
  },
  {
    id: 'client_name',
    header: 'Client Name',
    cell: ({ row }) => {
      const proposal = row.original;
      return <div>{getClientName(proposal)}</div>;
    },
  },
  {
    accessorKey: 'is_approved',
    header: 'Approved',
    cell: ({ row }) => {
      return (
        <ApprovalStatusSelector
          proposal={row.original}
          onApprovalChange={onApprovalChange}
        />
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const proposal = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={() => onViewProposal(proposal)}>
              <Eye className='mr-2 h-4 w-4' />
              View Proposal
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
