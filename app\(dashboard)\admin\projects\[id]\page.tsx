'use client';

import {
  Alert<PERSON>ircle,
  CircleCheck,
  CircleX,
  Edit,
  Globe,
  HelpCircle,
  MoreHorizontal,
  Plus,
  Save,
  Users,
  X,
} from 'lucide-react';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { DatePicker } from '@/components/projects/date-picker';
import { LeadSelector } from '@/components/projects/lead-selector';
import { PrioritySelector } from '@/components/projects/priority-selector';
import { getPercentComplete } from '@/components/projects/project-line';
import { ProjectMembersManager } from '@/components/projects/project-members-manager';
import { StatusWithPercent } from '@/components/projects/status-with-percent';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  useCycles,
  useIssues,
  useProjectMembers,
  useProjects,
  useTeams,
} from '@/hooks/use-db';
import type { Project, ProjectMember } from '@/lib/supabase/database-modules';

export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id as string;
  const { projects, loading, updateProject } = useProjects();
  const { teams } = useTeams();
  const { fetchProjectMembersByProject } = useProjectMembers();
  const { issues } = useIssues();
  const { cycles } = useCycles();

  const [project, setProject] = useState<Project | null>(null);
  const [healthUpdate, setHealthUpdate] = useState('');
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [editedName, setEditedName] = useState('');
  const [editedDescription, setEditedDescription] = useState('');
  const [projectMembers, setProjectMembers] = useState<ProjectMember[]>([]);
  const [membersLoading, setMembersLoading] = useState(false);

  useEffect(() => {
    if (projects.length > 0 && projectId) {
      const foundProject = projects.find((p) => p.id === projectId);
      setProject(foundProject || null);
      if (foundProject) {
        setEditedName(foundProject.name);
        setEditedDescription(foundProject.description || '');

        // Fetch project members
        setMembersLoading(true);
        fetchProjectMembersByProject(projectId)
          .then((members) => {
            setProjectMembers(members);
          })
          .catch((error) => {
            console.error('Failed to fetch project members:', error);
            toast.error('Failed to load project members');
          })
          .finally(() => {
            setMembersLoading(false);
          });
      }
    }
  }, [projects, projectId, fetchProjectMembersByProject]);

  // Update handlers
  const handleUpdateProject = (updates: Partial<Project>) => {
    if (!project) return;

    updateProject(project.id, updates)
      .then(() => {
        toast.success('Project updated successfully');
      })
      .catch((error) => {
        toast.error('Failed to update project');
        console.error('Error updating project:', error);
      });
  };

  const handleSaveName = () => {
    if (editedName.trim() && editedName !== project?.name) {
      handleUpdateProject({ name: editedName.trim() });
    }
    setIsEditingName(false);
  };

  const handleSaveDescription = () => {
    if (editedDescription !== project?.description) {
      handleUpdateProject({ description: editedDescription || null });
    }
    setIsEditingDescription(false);
  };

  const handleStatusChange = (statusId: string) => {
    handleUpdateProject({ status_id: statusId });
  };

  const handlePriorityChange = (priorityId: string) => {
    handleUpdateProject({ priority_id: priorityId });
  };

  const handleLeadChange = (leadId: string) => {
    handleUpdateProject({ lead_id: leadId });
  };

  const handleDateChange = (
    field: 'start_date' | 'target_date',
    date: Date | undefined
  ) => {
    const dateValue = date ? date.toISOString().split('T')[0] : null;
    handleUpdateProject({ [field]: dateValue });
  };

  const handleHealthChange = (healthId: string) => {
    const validHealthId = healthId as
      | 'no-update'
      | 'off-track'
      | 'on-track'
      | 'at-risk';
    handleUpdateProject({ health_id: validHealthId });
  };

  const getHealthIcon = (healthId: string) => {
    switch (healthId) {
      case 'on-track':
        return <CircleCheck className='size-4 text-green-500' />;
      case 'off-track':
        return <CircleX className='size-4 text-red-500' />;
      case 'at-risk':
        return <AlertCircle className='size-4 text-amber-500' />;
      default:
        return <HelpCircle className='size-4 text-muted-foreground' />;
    }
  };

  if (loading) {
    return <ProjectDetailSkeleton />;
  }

  if (!project) {
    return (
      <main className='mx-auto max-w-7xl w-full'>
        <div className='p-6'>Project not found</div>
      </main>
    );
  }

  return (
    <main className='mx-auto max-w-7xl w-full'>
      <header className='pl-12 h-11 inline-flex items-center border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Projects</p>
      </header>

      <div className='flex h-[calc(100vh-44px)]'>
        {/* Main Content */}
        <div className='flex-1 p-6'>
          {/* Project Header */}
          <div className='mb-6'>
            {/* Editable Project Name */}
            <div className='flex items-center gap-2 mb-2'>
              {isEditingName ? (
                <div className='flex items-center gap-2'>
                  <Input
                    value={editedName}
                    onChange={(e) => setEditedName(e.target.value)}
                    className='text-2xl font-semibold border-none p-0 h-auto'
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleSaveName();
                      if (e.key === 'Escape') {
                        setIsEditingName(false);
                        setEditedName(project.name);
                      }
                    }}
                    autoFocus
                  />
                  <Button size='sm' onClick={handleSaveName}>
                    <Save className='size-3' />
                  </Button>
                  <Button
                    size='sm'
                    variant='ghost'
                    onClick={() => {
                      setIsEditingName(false);
                      setEditedName(project.name);
                    }}
                  >
                    <X className='size-3' />
                  </Button>
                </div>
              ) : (
                <div className='flex items-center gap-2'>
                  <h1 className='text-2xl font-semibold'>{project.name}</h1>
                  <Button
                    size='sm'
                    variant='ghost'
                    onClick={() => setIsEditingName(true)}
                  >
                    <Edit className='size-3' />
                  </Button>
                </div>
              )}
            </div>

            {/* Editable Description */}
            <div className='mb-4'>
              {isEditingDescription ? (
                <div className='flex items-start gap-2'>
                  <Textarea
                    value={editedDescription}
                    onChange={(e) => setEditedDescription(e.target.value)}
                    className='text-sm text-muted-foreground resize-none'
                    placeholder='Add a short summary...'
                    rows={2}
                  />
                  <div className='flex flex-col gap-1'>
                    <Button size='sm' onClick={handleSaveDescription}>
                      <Save className='size-3' />
                    </Button>
                    <Button
                      size='sm'
                      variant='ghost'
                      onClick={() => {
                        setIsEditingDescription(false);
                        setEditedDescription(project.description || '');
                      }}
                    >
                      <X className='size-3' />
                    </Button>
                  </div>
                </div>
              ) : (
                <div className='flex items-start gap-2'>
                  <p className='text-muted-foreground text-sm flex-1'>
                    {project.description || 'Add a short summary...'}
                  </p>
                  <Button
                    size='sm'
                    variant='ghost'
                    onClick={() => setIsEditingDescription(true)}
                  >
                    <Edit className='size-3' />
                  </Button>
                </div>
              )}
            </div>

            {/* Properties Row */}
            <div className='flex items-center gap-4 mb-6 flex-wrap'>
              <div className='flex items-center gap-2'>
                <span className='text-sm text-muted-foreground'>
                  Properties
                </span>
              </div>

              {/* Status */}
              <div className='flex items-center gap-2'>
                <StatusWithPercent
                  status={
                    project.status
                      ? {
                          id: project.status.id,
                          name: project.status.name,
                          color: project.status.color,
                          sort_order: 0,
                        }
                      : null
                  }
                  percentComplete={getPercentComplete(project.status)}
                  onStatusChange={handleStatusChange}
                />
              </div>

              {/* Priority */}
              <div className='flex items-center gap-2'>
                <PrioritySelector
                  priority={
                    project.priority
                      ? {
                          id: project.priority.id,
                          name: project.priority.name,
                          icon_name: project.priority.icon_name || '',
                          sort_order: project.priority.sort_order || 0,
                        }
                      : null
                  }
                  onPriorityChange={handlePriorityChange}
                />
              </div>

              {/* Lead */}
              <div className='flex items-center gap-2'>
                <LeadSelector
                  lead={
                    project.lead
                      ? {
                          id: project.lead.id,
                          name: project.lead.full_name || project.lead.email,
                          email: project.lead.email,
                          avatar_url: project.lead.avatar_url,
                        }
                      : null
                  }
                  onLeadChange={handleLeadChange}
                />
              </div>

              {/* Start Date */}
              <div className='flex items-center gap-2'>
                <DatePicker
                  date={
                    project.start_date
                      ? new Date(project.start_date)
                      : undefined
                  }
                  onDateChange={(date) => handleDateChange('start_date', date)}
                />
              </div>

              {/* Target Date */}
              <div className='flex items-center gap-2'>
                <DatePicker
                  date={
                    project.target_date
                      ? new Date(project.target_date)
                      : undefined
                  }
                  onDateChange={(date) => handleDateChange('target_date', date)}
                />
              </div>
            </div>

            {/* Team Section */}
            {teams.length > 0 && (
              <div className='flex items-center gap-2 mb-6'>
                <span className='text-sm text-muted-foreground'>Teams</span>
                {teams.slice(0, 3).map((team) => (
                  <Button
                    key={team.id}
                    variant='ghost'
                    size='sm'
                    className='h-7 px-2'
                  >
                    <Users className='size-3 mr-1' />
                    {team.name}
                  </Button>
                ))}
                {teams.length > 3 && (
                  <Button variant='ghost' size='sm' className='h-7 px-1'>
                    <MoreHorizontal className='size-3' />
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Resources Section */}
          <div className='mb-6'>
            <div className='flex items-center gap-2 mb-3'>
              <span className='text-sm text-muted-foreground'>Resources</span>
              <Button variant='ghost' size='sm' className='h-7 px-2'>
                <Globe className='size-3 mr-1' />
                Project Repository
              </Button>
              <Button variant='ghost' size='sm' className='h-7 px-1'>
                <Plus className='size-3' />
              </Button>
            </div>
          </div>

          {/* Health Status Update */}
          <div className='border  p-4'>
            <div className='flex items-center justify-between mb-3'>
              <div className='flex items-center gap-2'>
                {getHealthIcon(project.health?.id || 'no-update')}
                <span className='text-sm font-medium'>
                  {project.health?.name || 'No Update'}
                </span>
              </div>
              <div className='flex gap-2'>
                {['on-track', 'at-risk', 'off-track', 'no-update'].map(
                  (healthId) => (
                    <Button
                      key={healthId}
                      variant={
                        project.health?.id === healthId ? 'default' : 'outline'
                      }
                      size='sm'
                      onClick={() => handleHealthChange(healthId)}
                      className='h-7 px-2'
                    >
                      {getHealthIcon(healthId)}
                    </Button>
                  )
                )}
              </div>
            </div>
            <Textarea
              placeholder='Write project update...'
              value={healthUpdate}
              onChange={(e) => setHealthUpdate(e.target.value)}
              className='min-h-[100px] resize-none'
            />
            {healthUpdate && (
              <div className='flex justify-end mt-2'>
                <Button
                  size='sm'
                  onClick={() => {
                    // Here you could save the health update to a project_updates table
                    toast.success('Project update saved');
                    setHealthUpdate('');
                  }}
                >
                  Post Update
                </Button>
              </div>
            )}
          </div>

          {/* Project Tasks/Issues */}
          <div className='mt-8'>
            <div className='flex items-center justify-between mb-4'>
              <h2 className='text-lg font-semibold'>Tasks & Issues</h2>
              <div className='text-sm text-muted-foreground'>
                {
                  issues.filter((issue) => issue.project_id === project.id)
                    .length
                }{' '}
                tasks
              </div>
            </div>

            <div className='border '>
              {(() => {
                const projectIssues = issues.filter(
                  (issue) => issue.project_id === project.id
                );

                if (projectIssues.length === 0) {
                  return (
                    <div className='p-6 text-center text-muted-foreground'>
                      <div className='text-sm'>
                        No tasks or issues found for this project.
                      </div>
                      <div className='text-xs mt-1'>
                        Create issues to track project progress.
                      </div>
                    </div>
                  );
                }

                return (
                  <div className='divide-y'>
                    {projectIssues.slice(0, 5).map((issue) => (
                      <div key={issue.id} className='p-4 hover:bg-muted/50'>
                        <div className='flex items-start justify-between'>
                          <div className='flex-1 min-w-0'>
                            <div className='flex items-center gap-2 mb-1'>
                              <span className='text-xs font-mono text-muted-foreground'>
                                {issue.identifier}
                              </span>
                              {issue.priority && (
                                <span className='text-xs px-1.5 py-0.5 bg-muted text-muted-foreground'>
                                  {issue.priority.name}
                                </span>
                              )}
                            </div>
                            <h4 className='font-medium text-sm truncate'>
                              {issue.title}
                            </h4>
                            <p className='text-xs text-muted-foreground mt-1 line-clamp-2'>
                              {issue.description || 'No description'}
                            </p>
                          </div>
                          <div className='flex items-center gap-2 ml-4'>
                            {issue.status && (
                              <span
                                className='text-xs px-2 py-1'
                                style={{
                                  backgroundColor: `${issue.status.color}20`,
                                  color: issue.status.color,
                                }}
                              >
                                {issue.status.name}
                              </span>
                            )}
                            {issue.assignee && (
                              <div className='w-6 h-6 bg-muted flex items-center justify-center text-xs'>
                                {issue.assignee.name?.charAt(0) || 'U'}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {projectIssues.length > 5 && (
                      <div className='p-3 text-center border-t bg-muted/20'>
                        <span className='text-xs text-muted-foreground'>
                          +{projectIssues.length - 5} more tasks
                        </span>
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className='w-80 border-l bg-muted/20 p-6'>
          <div className='space-y-6'>
            {/* Progress */}
            <div>
              <div className='text-sm font-medium text-muted-foreground mb-2'>
                Progress
              </div>
              <div className='flex items-center gap-2'>
                <div className='flex-1 bg-muted h-2'>
                  <div
                    className='bg-primary h-2 transition-all'
                    style={{ width: `${getPercentComplete(project.status)}%` }}
                  />
                </div>
                <span className='text-sm font-medium'>
                  {getPercentComplete(project.status)}%
                </span>
              </div>
            </div>

            {/* Start Date */}
            <div>
              <div className='text-sm font-medium text-muted-foreground mb-2'>
                Start Date
              </div>
              <DatePicker
                date={
                  project.start_date ? new Date(project.start_date) : undefined
                }
                onDateChange={(date) => handleDateChange('start_date', date)}
              />
            </div>

            {/* Target Date */}
            <div>
              <div className='text-sm font-medium text-muted-foreground mb-2'>
                Target Date
              </div>
              <DatePicker
                date={
                  project.target_date
                    ? new Date(project.target_date)
                    : undefined
                }
                onDateChange={(date) => handleDateChange('target_date', date)}
              />
            </div>

            {/* Teams */}
            {teams.length > 0 && (
              <div>
                <div className='text-sm font-medium text-muted-foreground mb-2'>
                  Teams
                </div>
                <div className='flex flex-wrap gap-1'>
                  {teams.map((team) => (
                    <Button
                      key={team.id}
                      variant='outline'
                      size='sm'
                      className='h-7 px-2 text-xs'
                    >
                      <Users className='size-3 mr-1' />
                      {team.name}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Health Status */}
            <div>
              <div className='text-sm font-medium text-muted-foreground mb-2'>
                Health Status
              </div>
              <div className='flex items-center gap-2'>
                {getHealthIcon(project.health?.id || 'no-update')}
                <span className='text-sm'>
                  {project.health?.name || 'No Update'}
                </span>
              </div>
              <p className='text-xs text-muted-foreground mt-1'>
                {project.health?.description || 'No health status description'}
              </p>
            </div>

            {/* Project Members */}
            <div>
              <div className='text-sm font-medium text-muted-foreground mb-2'>
                Project Members
              </div>
              {membersLoading ? (
                <div className='text-sm text-muted-foreground'>
                  Loading members...
                </div>
              ) : (
                <ProjectMembersManager
                  projectId={project.id}
                  projectMembers={projectMembers}
                  onMembersUpdate={() => {
                    setMembersLoading(true);
                    fetchProjectMembersByProject(projectId)
                      .then((members) => {
                        setProjectMembers(members);
                      })
                      .catch((error) => {
                        console.error(
                          'Failed to fetch project members:',
                          error
                        );
                        toast.error('Failed to load project members');
                      })
                      .finally(() => {
                        setMembersLoading(false);
                      });
                  }}
                />
              )}
            </div>

            {/* Project Cycles */}
            <div>
              <div className='text-sm font-medium text-muted-foreground mb-2'>
                Development Cycles
              </div>
              {(() => {
                const projectCycles = cycles.filter((cycle) =>
                  issues.some(
                    (issue) =>
                      issue.project_id === project.id &&
                      issue.cycle_id === cycle.id
                  )
                );

                if (projectCycles.length === 0) {
                  return (
                    <div className='text-sm text-muted-foreground'>
                      No cycles associated with this project
                    </div>
                  );
                }

                return (
                  <div className='space-y-2'>
                    {projectCycles.slice(0, 3).map((cycle) => (
                      <div key={cycle.id} className='p-2 border text-sm'>
                        <div className='font-medium'>Cycle {cycle.number}</div>
                        <div className='text-xs text-muted-foreground'>
                          {cycle.name}
                        </div>
                        <div className='text-xs text-muted-foreground mt-1'>
                          Progress: {cycle.progress}%
                        </div>
                      </div>
                    ))}
                    {projectCycles.length > 3 && (
                      <div className='text-xs text-muted-foreground'>
                        +{projectCycles.length - 3} more cycles
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>

            {/* Project Metadata */}
            <div>
              <div className='text-sm font-medium text-muted-foreground mb-2'>
                Created
              </div>
              <div className='text-sm'>
                {new Date(project.created_at).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                })}
              </div>
            </div>

            <div>
              <div className='text-sm font-medium text-muted-foreground mb-2'>
                Last Updated
              </div>
              <div className='text-sm'>
                {new Date(project.updated_at).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

export function ProjectDetailSkeleton() {
  return (
    <main className='mx-auto max-w-7xl w-full'>
      {/* Header */}
      <header className='pl-12 h-11 inline-flex items-center border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='h-4 w-20 bg-muted rounded animate-pulse' />
      </header>

      <div className='flex h-[calc(100vh-44px)]'>
        {/* Main Content */}
        <div className='flex-1 p-6'>
          {/* Project Header */}
          <div className='mb-6'>
            {/* Project Name */}
            <div className='flex items-center gap-2 mb-2'>
              <div className='h-8 w-48 bg-muted rounded animate-pulse' />
              <div className='h-7 w-7 bg-muted rounded animate-pulse' />
            </div>

            {/* Description */}
            <div className='mb-4'>
              <div className='flex items-start gap-2'>
                <div className='flex-1 space-y-2'>
                  <div className='h-4 w-full bg-muted rounded animate-pulse' />
                  <div className='h-4 w-3/4 bg-muted rounded animate-pulse' />
                </div>
                <div className='h-7 w-7 bg-muted rounded animate-pulse' />
              </div>
            </div>

            {/* Properties Row */}
            <div className='flex items-center gap-4 mb-6 flex-wrap'>
              <div className='h-4 w-20 bg-muted rounded animate-pulse' />
              <div className='h-7 w-24 bg-muted rounded animate-pulse' />
              <div className='h-7 w-20 bg-muted rounded animate-pulse' />
              <div className='h-7 w-28 bg-muted rounded animate-pulse' />
              <div className='h-7 w-32 bg-muted rounded animate-pulse' />
              <div className='h-7 w-32 bg-muted rounded animate-pulse' />
            </div>

            {/* Teams */}
            <div className='flex items-center gap-2 mb-6'>
              <div className='h-4 w-16 bg-muted rounded animate-pulse' />
              <div className='h-7 w-20 bg-muted rounded animate-pulse' />
              <div className='h-7 w-24 bg-muted rounded animate-pulse' />
              <div className='h-7 w-16 bg-muted rounded animate-pulse' />
            </div>

            {/* Resources */}
            <div className='mb-6'>
              <div className='flex items-center gap-2 mb-3'>
                <div className='h-4 w-20 bg-muted rounded animate-pulse' />
                <div className='h-7 w-32 bg-muted rounded animate-pulse' />
                <div className='h-7 w-7 bg-muted rounded animate-pulse' />
              </div>
            </div>

            {/* Health Status */}
            <div className='border p-4 rounded'>
              <div className='flex items-center justify-between mb-3'>
                <div className='flex items-center gap-2'>
                  <div className='h-4 w-4 bg-muted rounded-full animate-pulse' />
                  <div className='h-4 w-24 bg-muted rounded animate-pulse' />
                </div>
                <div className='flex gap-2'>
                  {Array(4)
                    .fill(0)
                    .map((_, i) => (
                      <div
                        key={i}
                        className='h-7 w-7 bg-muted rounded animate-pulse'
                      />
                    ))}
                </div>
              </div>
              <div className='h-24 w-full bg-muted rounded animate-pulse' />
            </div>

            {/* Tasks/Issues */}
            <div className='mt-8'>
              <div className='flex items-center justify-between mb-4'>
                <div className='h-6 w-32 bg-muted rounded animate-pulse' />
                <div className='h-4 w-16 bg-muted rounded animate-pulse' />
              </div>
              <div className='border rounded'>
                <div className='divide-y'>
                  {Array(5)
                    .fill(0)
                    .map((_, i) => (
                      <div key={i} className='p-4'>
                        <div className='flex items-start justify-between'>
                          <div className='flex-1 min-w-0 space-y-2'>
                            <div className='flex items-center gap-2 mb-1'>
                              <div className='h-4 w-16 bg-muted rounded animate-pulse' />
                              <div className='h-4 w-12 bg-muted rounded animate-pulse' />
                            </div>
                            <div className='h-5 w-3/4 bg-muted rounded animate-pulse' />
                            <div className='h-4 w-full bg-muted rounded animate-pulse' />
                            <div className='h-4 w-5/6 bg-muted rounded animate-pulse' />
                          </div>
                          <div className='flex items-center gap-2 ml-4'>
                            <div className='h-4 w-16 bg-muted rounded animate-pulse' />
                            <div className='h-6 w-6 bg-muted rounded-full animate-pulse' />
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className='w-80 border-l bg-muted/20 p-6'>
          <div className='space-y-6'>
            {/* Progress */}
            <div>
              <div className='h-4 w-20 bg-muted rounded animate-pulse mb-2' />
              <div className='flex items-center gap-2'>
                <div className='flex-1 bg-muted h-2 rounded animate-pulse' />
                <div className='h-4 w-12 bg-muted rounded animate-pulse' />
              </div>
            </div>

            {/* Start Date */}
            <div>
              <div className='h-4 w-24 bg-muted rounded animate-pulse mb-2' />
              <div className='h-7 w-32 bg-muted rounded animate-pulse' />
            </div>

            {/* Target Date */}
            <div>
              <div className='h-4 w-24 bg-muted rounded animate-pulse mb-2' />
              <div className='h-7 w-32 bg-muted rounded animate-pulse' />
            </div>

            {/* Teams */}
            <div>
              <div className='h-4 w-16 bg-muted rounded animate-pulse mb-2' />
              <div className='flex flex-wrap gap-1'>
                {Array(3)
                  .fill(0)
                  .map((_, i) => (
                    <div
                      key={i}
                      className='h-7 w-20 bg-muted rounded animate-pulse'
                    />
                  ))}
              </div>
            </div>

            {/* Health Status */}
            <div>
              <div className='h-4 w-24 bg-muted rounded animate-pulse mb-2' />
              <div className='flex items-center gap-2'>
                <div className='h-4 w-4 bg-muted rounded-full animate-pulse' />
                <div className='h-4 w-20 bg-muted rounded animate-pulse' />
              </div>
              <div className='h-4 w-full bg-muted rounded animate-pulse mt-1' />
            </div>

            {/* Project Members */}
            <div>
              <div className='h-4 w-28 bg-muted rounded animate-pulse mb-2' />
              <div className='space-y-2'>
                {Array(3)
                  .fill(0)
                  .map((_, i) => (
                    <div
                      key={i}
                      className='h-8 w-full bg-muted rounded animate-pulse'
                    />
                  ))}
              </div>
            </div>

            {/* Project Cycles */}
            <div>
              <div className='h-4 w-32 bg-muted rounded animate-pulse mb-2' />
              <div className='space-y-2'>
                {Array(3)
                  .fill(0)
                  .map((_, i) => (
                    <div key={i} className='p-2 border rounded space-y-1'>
                      <div className='h-4 w-24 bg-muted rounded animate-pulse' />
                      <div className='h-3 w-20 bg-muted rounded animate-pulse' />
                      <div className='h-3 w-16 bg-muted rounded animate-pulse' />
                    </div>
                  ))}
              </div>
            </div>

            {/* Created */}
            <div>
              <div className='h-4 w-20 bg-muted rounded animate-pulse mb-2' />
              <div className='h-4 w-24 bg-muted rounded animate-pulse' />
            </div>

            {/* Last Updated */}
            <div>
              <div className='h-4 w-24 bg-muted rounded animate-pulse mb-2' />
              <div className='h-4 w-24 bg-muted rounded animate-pulse' />
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
