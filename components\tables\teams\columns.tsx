'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { Edit, Eye, MoreHorizontal, Trash2, Users } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTeamMembers, useTeams } from '@/hooks/use-db';
import type { Team } from '@/lib/supabase/database-modules';

export const createColumns = (): ColumnDef<Team>[] => {
  return [
    {
      accessorKey: 'name',
      header: 'Team Name',
      cell: ({ row }) => {
        const team = row.original;
        return (
          <div className='flex items-center gap-3'>
            <div className='flex items-center justify-center w-8 h-8 rounded-lg bg-muted'>
              {team.icon ? (
                <span className='text-sm'>{team.icon}</span>
              ) : (
                <Users className='h-4 w-4' />
              )}
            </div>
            <div className='flex flex-col'>
              <Link
                href={`/admin/teams/${team.id}`}
                className='font-medium hover:underline'
              >
                {team.name}
              </Link>
              {team.description && (
                <span className='text-sm text-muted-foreground truncate max-w-[200px]'>
                  {team.description}
                </span>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'color',
      header: 'Color',
      cell: ({ row }) => {
        const team = row.original;
        return team.color ? (
          <div className='flex items-center gap-2'>
            <div
              className='w-4 h-4 rounded-full border'
              style={{ backgroundColor: team.color }}
            />
            <span className='text-sm text-muted-foreground'>{team.color}</span>
          </div>
        ) : (
          <span className='text-sm text-muted-foreground'>No color</span>
        );
      },
    },
    {
      id: 'members',
      header: 'Members',
      cell: ({ row }) => {
        const team = row.original;
        const { teamMembers } = useTeamMembers();

        const memberCount = teamMembers.filter(
          (member) => member.team_id === team.id && member.joined === true
        ).length;

        return (
          <Badge variant='secondary' className='font-normal'>
            {memberCount} {memberCount === 1 ? 'member' : 'members'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Created',
      cell: ({ row }) => {
        const team = row.original;
        return team.created_at ? (
          <span className='text-sm text-muted-foreground'>
            {new Date(team.created_at).toLocaleDateString()}
          </span>
        ) : (
          <span className='text-sm text-muted-foreground'>Unknown</span>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const team = row.original;
        const router = useRouter();
        const { deleteTeam } = useTeams();

        const handleViewTeam = () => {
          router.push(`/admin/teams/${team.id}`);
        };

        const handleEditTeam = () => {
          router.push(`/admin/teams/${team.id}/edit`);
        };

        const handleDeleteTeam = () => {
          if (
            confirm(`Are you sure you want to delete the team "${team.name}"?`)
          ) {
            deleteTeam(team.id)
              .then(() => {
                toast.success('Team deleted successfully');
              })
              .catch((error) => {
                toast.error('Failed to delete team');
                console.error('Error deleting team:', error);
              });
          }
        };

        return (
          <div className='flex items-center gap-2'>
            <Button
              variant='ghost'
              size='sm'
              className='h-7 w-7 p-0'
              onClick={handleViewTeam}
            >
              <Eye className='h-4 w-4' />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' size='sm' className='h-7 w-7 p-0'>
                  <MoreHorizontal className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem onClick={handleViewTeam}>
                  <Eye className='mr-2 h-4 w-4' />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleEditTeam}>
                  <Edit className='mr-2 h-4 w-4' />
                  Edit Team
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleDeleteTeam}
                  className='text-destructive'
                >
                  <Trash2 className='mr-2 h-4 w-4' />
                  Delete Team
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];
};
