/**
 * Utility functions for handling errors in the application
 */

/**
 * Extracts a meaningful error message from various error types
 * @param error - The error object (could be <PERSON><PERSON>r, Supabase error, or any object)
 * @param fallbackMessage - Default message if no specific error message is found
 * @returns A user-friendly error message string
 */
export function extractErrorMessage(error: unknown, fallbackMessage = 'An unexpected error occurred'): string {
  // Handle Error instances
  if (error instanceof Error) {
    return error.message;
  }
  
  // Handle Supabase errors and other objects with message property
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as Record<string, unknown>;
    
    // Check for message property
    if (typeof errorObj.message === 'string' && errorObj.message.trim()) {
      return errorObj.message;
    }
    
    // Check for details property (common in Supabase errors)
    if (typeof errorObj.details === 'string' && errorObj.details.trim()) {
      return errorObj.details;
    }
    
    // Check for hint property (common in Supabase errors)
    if (typeof errorObj.hint === 'string' && errorObj.hint.trim()) {
      return errorObj.hint;
    }
    
    // Check for error_description (OAuth errors)
    if (typeof errorObj.error_description === 'string' && errorObj.error_description.trim()) {
      return errorObj.error_description;
    }
  }
  
  // Handle string errors
  if (typeof error === 'string' && error.trim()) {
    return error;
  }
  
  return fallbackMessage;
}

/**
 * Creates a standardized Error object from various error types
 * @param error - The original error
 * @param fallbackMessage - Default message if no specific error message is found
 * @returns A proper Error instance
 */
export function createStandardError(error: unknown, fallbackMessage = 'An unexpected error occurred'): Error {
  const message = extractErrorMessage(error, fallbackMessage);
  
  // If it's already an Error instance with the same message, return it
  if (error instanceof Error && error.message === message) {
    return error;
  }
  
  // Create a new Error with the extracted message
  const standardError = new Error(message);
  
  // Preserve stack trace if available
  if (error instanceof Error && error.stack) {
    standardError.stack = error.stack;
  }
  
  return standardError;
}

/**
 * Logs an error with context information
 * @param context - Context string describing where the error occurred
 * @param error - The error to log
 * @param additionalInfo - Optional additional information to log
 */
export function logError(context: string, error: unknown, additionalInfo?: Record<string, unknown>): void {
  console.error(`[${context}]`, error);
  
  if (additionalInfo) {
    console.error(`[${context}] Additional info:`, additionalInfo);
  }
  
  // Log the extracted message for clarity
  const message = extractErrorMessage(error);
  if (message !== String(error)) {
    console.error(`[${context}] Extracted message:`, message);
  }
}

/**
 * Common error messages for database operations
 */
export const ERROR_MESSAGES = {
  // Authentication errors
  NOT_AUTHENTICATED: 'You must be logged in to perform this action',
  INSUFFICIENT_PERMISSIONS: 'You do not have permission to perform this action',
  
  // Database operation errors
  CREATE_FAILED: 'Failed to create record',
  UPDATE_FAILED: 'Failed to update record',
  DELETE_FAILED: 'Failed to delete record',
  FETCH_FAILED: 'Failed to fetch data',
  
  // Validation errors
  INVALID_INPUT: 'Invalid input provided',
  REQUIRED_FIELD_MISSING: 'Required field is missing',
  
  // Network errors
  NETWORK_ERROR: 'Network error occurred. Please check your connection.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  
  // Generic errors
  UNKNOWN_ERROR: 'An unexpected error occurred',
  TRY_AGAIN: 'Please try again later',
} as const;

/**
 * Checks if an error is a network-related error
 * @param error - The error to check
 * @returns True if it's likely a network error
 */
export function isNetworkError(error: unknown): boolean {
  const message = extractErrorMessage(error).toLowerCase();
  return (
    message.includes('network') ||
    message.includes('fetch') ||
    message.includes('connection') ||
    message.includes('timeout') ||
    message.includes('offline')
  );
}

/**
 * Checks if an error is an authentication-related error
 * @param error - The error to check
 * @returns True if it's likely an authentication error
 */
export function isAuthError(error: unknown): boolean {
  const message = extractErrorMessage(error).toLowerCase();
  return (
    message.includes('unauthorized') ||
    message.includes('authentication') ||
    message.includes('not authenticated') ||
    message.includes('login') ||
    message.includes('permission')
  );
}
