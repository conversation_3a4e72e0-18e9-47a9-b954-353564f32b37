'use client';

import { useMemo } from 'react';
import {
  TrendingDown,
  TrendingUp,
  <PERSON>,
  UserCheck,
  UserX,
  Clock,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { Application } from '@/lib/supabase/database-modules';

interface ApplicationsMetricsProps {
  applications: Application[];
  loading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  trend?: 'up' | 'down' | 'neutral';
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  icon?: React.ReactNode;
}

function MetricCard({
  title,
  value,
  subtitle,
  trend,
  variant = 'default',
  icon,
}: MetricCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'destructive':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      default:
        return 'border-border bg-card';
    }
  };

  const getTrendIcon = () => {
    if (trend === 'up')
      return <TrendingUp className='h-4 w-4 text-green-600' />;
    if (trend === 'down')
      return <TrendingDown className='h-4 w-4 text-red-600' />;
    return null;
  };

  return (
    <Card className={`${getVariantStyles()} transition-all`}>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium text-muted-foreground'>
          {title}
        </CardTitle>
        {icon && <div className='text-muted-foreground'>{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className='flex items-center justify-between'>
          <div>
            <div className='text-2xl font-bold'>{value}</div>
            <div className='flex items-center gap-1 text-xs text-muted-foreground'>
              {getTrendIcon()}
              <span>{subtitle}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function ApplicationsMetrics({
  applications,
  loading,
}: ApplicationsMetricsProps) {
  const metrics = useMemo(() => {
    if (!applications.length) {
      return {
        total: 0,
        pending: 0,
        reviewed: 0,
        accepted: 0,
        rejected: 0,
        recentApplications: 0,
      };
    }

    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const total = applications.length;
    const pending = applications.filter(
      (app) => app.reviewed === 'received'
    ).length;
    const reviewed = applications.filter(
      (app) => app.reviewed === 'reviewed'
    ).length;
    const accepted = applications.filter(
      (app) => app.approved === 'accepted'
    ).length;
    const rejected = applications.filter(
      (app) => app.reviewed === 'notAccepted' || app.approved === 'notAccepted'
    ).length;
    const recentApplications = applications.filter(
      (app) => new Date(app.created_at) >= lastWeek
    ).length;

    return {
      total,
      pending,
      reviewed,
      accepted,
      rejected,
      recentApplications,
    };
  }, [applications]);

  if (loading) {
    return (
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {['total', 'pending', 'accepted', 'rejected'].map((metric) => (
          <Card key={metric} className='animate-pulse'>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <div className='h-4 w-20 bg-muted'></div>
              <div className='h-4 w-4 bg-muted'></div>
            </CardHeader>
            <CardContent>
              <div className='h-8 w-16 bg-muted  mb-2'></div>
              <div className='h-3 w-24 bg-muted '></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const pendingPercentage =
    metrics.total > 0 ? Math.round((metrics.pending / metrics.total) * 100) : 0;
  const acceptanceRate =
    metrics.total > 0
      ? Math.round((metrics.accepted / metrics.total) * 100)
      : 0;

  return (
    <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
      <MetricCard
        title='Total Applications'
        value={metrics.total}
        subtitle={`${metrics.recentApplications} this week`}
        trend={metrics.recentApplications > 0 ? 'up' : 'neutral'}
        icon={<Users className='h-4 w-4' />}
      />

      <MetricCard
        title='Pending Review'
        value={metrics.pending}
        subtitle={`${pendingPercentage}% of total`}
        variant={metrics.pending > 5 ? 'warning' : 'default'}
        icon={<Clock className='h-4 w-4' />}
      />

      <MetricCard
        title='Accepted'
        value={metrics.accepted}
        subtitle={`${acceptanceRate}% acceptance rate`}
        variant='success'
        trend={
          acceptanceRate > 50 ? 'up' : acceptanceRate < 30 ? 'down' : 'neutral'
        }
        icon={<UserCheck className='h-4 w-4' />}
      />

      <MetricCard
        title='Rejected'
        value={metrics.rejected}
        subtitle='Not accepted'
        variant={
          metrics.rejected > metrics.accepted ? 'destructive' : 'default'
        }
        icon={<UserX className='h-4 w-4' />}
      />
    </div>
  );
}
