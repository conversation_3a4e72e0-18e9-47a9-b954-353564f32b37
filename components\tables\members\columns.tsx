'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Eye, MoreHorizontal, UserX } from 'lucide-react';
import { format } from 'date-fns';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import type { Member } from '@/lib/supabase/database-modules';

import { MemberRoleSelector } from '@/components/members/member-role-selector';
import { MemberStatusDisplay } from '@/components/members/member-status-selector';

interface ColumnsProps {
  onViewMember: (member: Member) => void;
  onDeleteMember: (member: Member) => void;
  onRoleChange: (
    memberId: string,
    role: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'
  ) => Promise<void>;
}

export const createColumns = ({
  onViewMember,
  onDeleteMember,
  onRoleChange,
}: ColumnsProps): ColumnDef<Member>[] => [
  {
    accessorKey: 'name',
    header: 'Member',
    cell: ({ row }) => {
      const member = row.original;
      const displayName = member.full_name || member.name;
      const initials = displayName
        .split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);

      return (
        <div className='flex items-center gap-3'>
          <Avatar className='h-8 w-8'>
            <AvatarImage src={member.avatar_url || ''} alt={displayName} />
            <AvatarFallback className='text-xs'>{initials}</AvatarFallback>
          </Avatar>
          <div className='flex flex-col'>
            <div className='font-medium'>{displayName}</div>
            <div className='text-sm text-muted-foreground'>{member.email}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'username',
    header: 'Username',
    cell: ({ row }) => {
      const username = row.getValue('username') as string;
      return (
        <div className='text-sm'>
          {username ? `@${username}` : 'No username'}
        </div>
      );
    },
  },
  {
    accessorKey: 'role',
    header: 'Role',
    cell: ({ row }) => {
      return (
        <MemberRoleSelector member={row.original} onRoleChange={onRoleChange} />
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      return <MemberStatusDisplay member={row.original} />;
    },
  },
  {
    accessorKey: 'joined_date',
    header: 'Joined',
    cell: ({ row }) => {
      const joinedDate = row.getValue('joined_date') as string;
      const createdAt = row.original.created_at;
      const dateToShow = joinedDate || createdAt;

      if (!dateToShow) {
        return <div className='text-sm text-muted-foreground'>Unknown</div>;
      }

      const date = new Date(dateToShow);
      if (Number.isNaN(date.getTime())) {
        return (
          <div className='text-sm text-muted-foreground'>Invalid Date</div>
        );
      }

      return <div className='text-sm'>{format(date, 'MMM dd, yyyy')}</div>;
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const member = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={() => onViewMember(member)}>
              <Eye className='mr-2 h-4 w-4' />
              View Member
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onDeleteMember(member)}
              className='text-destructive'
            >
              <UserX className='mr-2 h-4 w-4' />
              Remove Member
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
