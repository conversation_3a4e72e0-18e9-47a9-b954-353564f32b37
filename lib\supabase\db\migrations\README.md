# Mock Data Migrations

This folder contains SQL migration files to add mock data to the database for testing and development purposes.

## How to Apply Migrations

You can apply these migrations using the Supabase Dashboard or the Supabase CLI.

### Using Supabase Dashboard

1. Go to the Supabase Dashboard
2. Select your project
3. Go to the SQL Editor
4. Copy and paste the content of each migration file
5. Run the SQL queries

### Using Supabase CLI

1. Install the Supabase CLI if you haven't already:
   ```bash
   npm install -g supabase
   ```

2. Login to Supabase:
   ```bash
   supabase login
   ```

3. Link your project:
   ```bash
   supabase link --project-ref <your-project-ref>
   ```

4. Apply the migrations:
   ```bash
   supabase db push
   ```

## Migration Files

- `add_mock_documents.sql`: Adds mock documents
- `add_mock_document_tags.sql`: Adds mock document tags
- `add_mock_contracts.sql`: Adds mock contract templates and smart contracts
- `add_mock_lawyers.sql`: Adds mock lawyers and consultations
- `add_mock_collaboration.sql`: Adds mock collaboration projects and tasks
- `add_mock_settings.sql`: Adds mock user settings and wallet connections

## Order of Execution

It's recommended to apply the migrations in the following order:

1. `add_mock_document_tags.sql`
2. `add_mock_documents.sql`
3. `add_mock_contracts.sql`
4. `add_mock_lawyers.sql`
5. `add_mock_collaboration.sql`
6. `add_mock_settings.sql`

## Notes

- These migrations use `auth.uid()` to get the current user's ID, so make sure you're logged in when applying them.
- Some migrations have dependencies on others, so applying them in the recommended order is important.
- If you encounter any errors, check the error message and make sure all required tables exist and have the correct schema.
