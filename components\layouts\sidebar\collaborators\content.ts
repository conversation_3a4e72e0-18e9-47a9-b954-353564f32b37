import {
  Box,
  DollarSign,
  FileText,
  Home,
  Inbox,
  Layers,
  Shield,
  Tag,
  Zap,
} from 'lucide-react';
import type { SidebarData } from '@/lib/supabase/database-modules';
import type { Database } from '@/lib/supabase/database-types';

type Profile_Types = Database['public']['Tables']['profiles']['Row'];

const collaboratorsSidebarData = ({
  profile,
}: {
  profile: Profile_Types;
}): SidebarData => {
  const user = {
    name: profile.full_name,
    email: profile.email,
    avatar: '/avatars/collaborator.jpg',
    role: profile.role,
  };
  const teams = [
    {
      name: 'collaborator Panel',
      logo: Shield,
      plan: 'Enterprise',
    },
  ];
  const navHeader = [
    {
      title: 'Home',
      url: '/collaborator',
      icon: Home,
    },
    {
      title: 'Inbox',
      url: '/collaborator/inbox',
      icon: Inbox,
    },
  ];
  const navMain = [
    {
      title: 'workspace',
      items: [
        {
          title: 'Projects',
          url: '/collaborator/projects',
          icon: Box,
          isActive: true,
        },
        {
          title: 'Issues',
          url: '/collaborator/issues',
          icon: Layers,
          isActive: true,
        },
        {
          title: 'Tasks',
          url: '/collaborator/tasks',
          icon: Tag,
          isActive: true,
        },
      ],
    },
    {
      title: 'Finance',
      items: [
        {
          title: 'Revenue',
          url: '/collaborator/revenue',
          icon: Zap,
          isActive: true,
        },
        {
          title: 'Payouts',
          url: '/collaborator/payouts',
          icon: DollarSign,
          isActive: true,
        },
        {
          title: 'Invoicing',
          url: '/collaborator/invoicing',
          icon: FileText,
          isActive: true,
        },
      ],
    },
    {
      title: 'Profile',
      items: [
        {
          title: 'Portfolio',
          url: '/collaborator/revenue',
          icon: Zap,
          isActive: true,
        },
        {
          title: 'Quote',
          url: '/collaborator/quote',
          icon: DollarSign,
          isActive: true,
        },
      ],
    },
    {
      title: 'Legal',
      items: [
        {
          title: 'NDA Agreement',
          url: '/affiliate/nda',
          icon: Shield,
          isActive: true,
        },
      ],
    },
  ];

  return {
    user,
    teams,
    navMain,
    navHeader,
  };
};

export default collaboratorsSidebarData;
