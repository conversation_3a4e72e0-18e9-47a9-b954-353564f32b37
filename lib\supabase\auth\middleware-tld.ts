import { createServerClient } from '@supabase/ssr';
import { type NextRequest, NextResponse } from 'next/server';
import type { Database } from '../database-types';

type UserRole = Database['public']['Enums']['Role Types'];

// Routes that require specific roles
const roleProtectedRoutes: Record<string, UserRole[]> = {
  '/admin': ['Admin'],
  '/affiliate': ['Affiliate'],
  '/collaborator': ['Collaborator'],
  '/volunteer': ['Volunteer'],
};

// Routes that any authenticated user can access (username-based routes)
const protectedRoutes = ['/admin', '/affiliate', '/collaborator', '/volunteer'];

// Routes that don't require authentication
export const unprotectedRoutes = [
  '/', // Home page
  '/login', // Login page
  '/auth', // Auth callback routes
  '/shared', // Shared document pages
  '/docs', // Documentation
  '/icons', // Documentation
  '/pricing', // Pricing page
  '/about', // About page
  '/contact', // Contact page
  '/waitlist', // Waitlist page
  '/api', // API routes
  '/terms', // Terms page
  '/privacy', // Privacy page
  '/not-found', // 404 page
  '/unauthorized', // Unauthorized page
  '/auth-error', // Auth Error page
];

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) =>
            request.cookies.set(name, value)
          );
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        },
      },
    }
  );

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const pathname = request.nextUrl.pathname;

  // Check for undefined routes
  if (pathname.includes('/undefined')) {
    const url = request.nextUrl.clone();
    if (!user) {
      // User is not logged in, redirect to login
      url.pathname = '/login';
    } else {
      // User is logged in, redirect to not found handler
      url.pathname = '/not-found-handler';
    }
    return NextResponse.redirect(url);
  }

  // Check if this is a top-level route (like /something)
  const isTopLevelRoute = /^\/[^/]+$/.test(pathname);

  // Check if this is an unprotected route
  const isUnprotectedRoute = unprotectedRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`)
  );

  // If it's an unprotected route, handle based on auth status
  if (isUnprotectedRoute) {
    // If user is logged in and trying to access login or create-account pages,
    // redirect them to their dashboard (but not for auth callback routes)
    if (
      user &&
      (pathname === '/login' || pathname === '/create-account') &&
      !pathname.startsWith('/auth/')
    ) {
      // Get user profile to redirect to their dashboard
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('username')
        .eq('id', user.id)
        .single();

      if (userProfile?.username) {
        const url = request.nextUrl.clone();
        url.pathname = `/${userProfile.username}`;
        return NextResponse.redirect(url);
      }
    }

    // For other unprotected routes, allow access
    return supabaseResponse;
  }

  // If this is a top-level route that's not in our unprotected list
  if (isTopLevelRoute && !isUnprotectedRoute) {
    // If there's a user logged in, check if this might be their username route
    if (user) {
      // Get user profile
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      // Check if this path matches the logged-in user's username
      if (pathname === `/${userProfile?.username}`) {
        // This is the user's profile route, let it proceed
        // Continue with the rest of middleware checks
      } else {
        // Now check if this path could be any valid username
        const username = pathname.substring(1); // Remove the leading slash
        const { data: matchedProfile } = await supabase
          .from('profiles')
          .select('username')
          .eq('username', username)
          .single();

        if (matchedProfile) {
          // This is a valid username route, let it proceed
        } else {
          // This is not a valid username or allowed route, show 404
          return NextResponse.rewrite(
            new URL('/not-found-handler', request.url)
          );
        }
      }
    } else {
      // No user is logged in, check if this could be any valid username
      const username = pathname.substring(1); // Remove the leading slash
      const { data: matchedProfile } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', username)
        .single();

      if (matchedProfile) {
        // This is a valid username, redirect to not-found (since we don't want
        // to allow anonymous access to user profiles based on your requirements)
        return NextResponse.rewrite(new URL('/not-found-handler', request.url));
      } else {
        // Not a valid username or allowed route, show 404
        return NextResponse.rewrite(new URL('/not-found-handler', request.url));
      }
    }
  }

  // Get path segments to analyze the route structure
  const segments = pathname.split('/').filter(Boolean);
  const isUsernameRoute = segments.length > 0;

  // If user is not logged in
  if (!user) {
    console.log('No user found, checking route type:', pathname);

    // Check if this is a protected route pattern
    const isProtectedRoutePattern = protectedRoutes.some((route) => {
      const routePattern = route.replace('[username]', '[^/]+');
      const regex = new RegExp(`^${routePattern}(/.*)?$`);
      const isMatch = regex.test(pathname);
      if (isMatch) {
        console.log(`Route ${pathname} matches protected pattern ${route}`);
      }
      return isMatch;
    });

    // Check if this is an unprotected route
    const isUnprotectedRoute = unprotectedRoutes.some((route) => {
      const isMatch = pathname === route || pathname.startsWith(`${route}/`);
      if (isMatch) {
        console.log(`Route ${pathname} matches unprotected route ${route}`);
      }
      return isMatch;
    });

    if (isProtectedRoutePattern) {
      // Redirect to login for protected routes when not logged in
      console.log(`Redirecting to login from protected route: ${pathname}`);
      const url = request.nextUrl.clone();
      url.pathname = '/login';
      return NextResponse.redirect(url);
    }

    if (!isUnprotectedRoute) {
      // For any route that's neither protected nor unprotected, show not found
      console.log(`Showing not-found for unknown route: ${pathname}`);
      return NextResponse.rewrite(new URL('/not-found-handler', request.url));
    }

    // If it's an unprotected route, allow access
    console.log(`Allowing access to unprotected route: ${pathname}`);
  }

  if (user) {
    // Get user profile
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (!userProfile) {
      // If profile doesn't exist, something is wrong
      const url = request.nextUrl.clone();
      url.pathname = '/login';
      return NextResponse.redirect(url);
    }

    // Check if this is a username route
    if (isUsernameRoute) {
      const routeUsername = segments[0];

      // If trying to access another user's routes
      if (routeUsername !== userProfile.username) {
        // Check if the username exists
        const { data: otherProfile } = await supabase
          .from('profiles')
          .select('username')
          .eq('username', routeUsername)
          .single();

        if (otherProfile) {
          // Username exists but it's not the current user
          const url = request.nextUrl.clone();
          url.pathname = '/unauthorized';
          return NextResponse.redirect(url);
        } else {
          // Username doesn't exist
          return NextResponse.rewrite(
            new URL('/not-found-handler', request.url)
          );
        }
      }
    }

    // Check role-based access for protected routes
    for (const [route, allowedRoles] of Object.entries(roleProtectedRoutes)) {
      if (
        pathname.startsWith(route) &&
        !allowedRoles.includes(userProfile?.role || 'user')
      ) {
        const url = request.nextUrl.clone();
        url.pathname = '/unauthorized';
        return NextResponse.redirect(url);
      }
    }
  }

  return supabaseResponse;
}
