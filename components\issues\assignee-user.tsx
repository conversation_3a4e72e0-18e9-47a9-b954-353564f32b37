'use client';

import { useState, useEffect } from 'react';
import { CheckIcon, CircleUserRound, Send, UserIcon } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { supabaseClient } from '@/lib/supabase/auth/client';
import type { IssueUser } from '@/lib/supabase/database-modules';

interface UserOption {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
  status?: 'online' | 'offline' | 'away';
}

interface AssigneeUserProps {
  user: UserOption | null;
  issueId?: string;
  onChange?: (user: UserOption | null) => void;
}

export function AssigneeUser({ user, issueId, onChange }: AssigneeUserProps) {
  const [open, setOpen] = useState(false);
  const [currentAssignee, setCurrentAssignee] = useState<UserOption | null>(
    user
  );
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setCurrentAssignee(user);
  }, [user]);

  // Load user options from Supabase profiles table (collaborators only)
  useEffect(() => {
    const loadUserOptions = async () => {
      try {
        setLoading(true);

        const { data, error } = await supabaseClient
          .from('profiles')
          .select('id, full_name, email, avatar_url, status')
          .eq('role', 'Collaborator')
          .order('full_name');

        if (error) {
          console.error('Failed to load user options:', error);
        } else {
          const users =
            data?.map((profile) => ({
              id: profile.id,
              name: profile.full_name || profile.email,
              email: profile.email,
              avatar_url: profile.avatar_url,
              status:
                (profile.status as 'online' | 'offline' | 'away') || 'offline',
            })) || [];
          setUserOptions(users);
        }
      } catch (error) {
        console.error('Failed to load user options:', error);
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      loadUserOptions();
    }
  }, [open]);

  const handleAssigneeChange = (newAssignee: UserOption | null) => {
    setCurrentAssignee(newAssignee);
    setOpen(false);
    if (onChange) {
      onChange(newAssignee);
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'online':
        return '#10b981';
      case 'away':
        return '#f59e0b';
      case 'offline':
      default:
        return '#6b7280';
    }
  };

  const renderAvatar = () => {
    if (currentAssignee) {
      return (
        <Avatar className='size-6 shrink-0'>
          <AvatarImage
            src={currentAssignee.avatar_url || undefined}
            alt={currentAssignee.name}
          />
          <AvatarFallback>{currentAssignee.name[0]}</AvatarFallback>
        </Avatar>
      );
    } else {
      return (
        <div className='size-6 flex items-center justify-center'>
          <CircleUserRound className='size-5 text-zinc-600' />
        </div>
      );
    }
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <button type='button' className='relative w-fit focus:outline-none'>
          {renderAvatar()}
          {currentAssignee && (
            <span
              className='border-background absolute -end-0.5 -bottom-0.5 size-2.5 rounded-full border-2'
              style={{
                backgroundColor: getStatusColor(currentAssignee.status),
              }}
            >
              <span className='sr-only'>{currentAssignee.status}</span>
            </span>
          )}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start' className='w-[206px]'>
        <DropdownMenuLabel>Assign to...</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleAssigneeChange(null);
          }}
        >
          <div className='flex items-center gap-2'>
            <UserIcon className='h-5 w-5' />
            <span>No assignee</span>
          </div>
          {!currentAssignee && <CheckIcon className='ml-auto h-4 w-4' />}
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        {loading ? (
          <DropdownMenuItem disabled>
            <span>Loading users...</span>
          </DropdownMenuItem>
        ) : (
          userOptions.map((user) => (
            <DropdownMenuItem
              key={user.id}
              onClick={(e) => {
                e.stopPropagation();
                handleAssigneeChange(user);
              }}
            >
              <div className='flex items-center gap-2'>
                <Avatar className='h-5 w-5'>
                  <AvatarImage
                    src={user.avatar_url || undefined}
                    alt={user.name}
                  />
                  <AvatarFallback>{user.name[0]}</AvatarFallback>
                </Avatar>
                <span>{user.name}</span>
              </div>
              {currentAssignee?.id === user.id && (
                <CheckIcon className='ml-auto h-4 w-4' />
              )}
            </DropdownMenuItem>
          ))
        )}
        <DropdownMenuSeparator />
        <DropdownMenuLabel>New user</DropdownMenuLabel>
        <DropdownMenuItem>
          <div className='flex items-center gap-2'>
            <Send className='h-4 w-4' />
            <span>Invite and assign...</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
