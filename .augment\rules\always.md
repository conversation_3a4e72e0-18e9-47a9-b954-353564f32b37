---
type: "always_apply"
---

When I create utility functions:
- Always use regular objects with methods instead of classes with only static methods
- Prefer: const Utils = { method: () => {} }
- Avoid: class Utils { static method() {} }
- Ask me if I want separate exported functions instead of a utility object# Cursor Rules for Clean Code and Complete Implementations

## Async/Await Rules

### Rule 1: Async Function Validation
- **NEVER** declare a function as `async` unless it contains at least one `await` expression
- **ALWAYS** remove the `async` modifier if no `await` is present in the function body
- **ALWAYS** use `await` when calling async functions or Promise-based operations

```typescript
// ❌ BAD - async without await
const handleSubmit = async (data: FormData) => {
  return processData(data);
};

// ✅ GOOD - either remove async or add await
const handleSubmit = (data: FormData) => {
  return processData(data);
};

// ✅ GOOD - async with await
const handleSubmit = async (data: FormData) => {
  return await processData(data);
};
```

### Rule 2: Promise Handling
- **ALWAYS** use `await` for Promise-based operations (API calls, database queries, etc.)
- **ALWAYS** wrap async operations in try-catch blocks for error handling
- **NEVER** return raw Promises from async functions without awaiting them

```typescript
// ✅ GOOD - proper async implementation
const fetchUserData = async (userId: string) => {
  try {
    const response = await fetch(`/api/users/${userId}`);
    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Failed to fetch user data:', error);
    throw error;
  }
};
```

## Complete Implementation Rules

### Rule 3: No Placeholder Code
- **NEVER** use "TODO", "Coming Soon", "To be implemented", or similar placeholders
- **ALWAYS** implement complete, functional features
- **ALWAYS** provide working code with proper logic flow

### Rule 4: Functional Components and Pages
- **ALWAYS** create fully functional React components with complete logic
- **ALWAYS** implement proper state management using hooks
- **ALWAYS** include proper event handlers and data processing
- **NEVER** leave empty function bodies or placeholder handlers

```typescript
// ❌ BAD - placeholder implementation
const UserProfile = () => {
  const handleSave = () => {
    // TODO: Implement save functionality
  };

  return (
    <div>
      <button onClick={handleSave}>Save</button>
      {/* Coming soon: User details */}
    </div>
  );
};

// ✅ GOOD - complete implementation
const UserProfile = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSave = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      await updateUser(user.id, user);
      // Show success message
    } catch (error) {
      // Handle error
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {user ? (
        <div>
          <input 
            value={user.name} 
            onChange={(e) => setUser({...user, name: e.target.value})}
          />
          <button onClick={handleSave} disabled={loading}>
            {loading ? 'Saving...' : 'Save'}
          </button>
        </div>
      ) : (
        <div>Loading...</div>
      )}
    </div>
  );
};
```


### Rule 5: Form Handling
- **ALWAYS** implement complete form validation
- **ALWAYS** handle form submission with proper error handling
- **ALWAYS** provide user feedback (loading states, success/error messages)
- **NEVER** leave form handlers empty or with placeholder logic

### Rule 6: API Integration
- **ALWAYS** implement complete API integration with proper error handling
- **ALWAYS** handle loading states and user feedback
- **ALWAYS** validate API responses and handle edge cases
- **NEVER** leave API calls incomplete or with mock data

### Rule 7: State Management
- **ALWAYS** implement proper state management for all interactive features
- **ALWAYS** handle state updates correctly with proper immutability
- **ALWAYS** manage loading, error, and success states
- **NEVER** leave state management incomplete or with hardcoded values

## Code Quality Rules

### Rule 8: Error Handling
- **ALWAYS** implement comprehensive error handling
- **ALWAYS** provide meaningful error messages to users
- **ALWAYS** log errors appropriately for debugging
- **NEVER** leave try-catch blocks empty or with generic error handling

### Rule 9: Type Safety
- **ALWAYS** use proper TypeScript types for all variables, functions, and components
- **ALWAYS** define interfaces for complex data structures
- **ALWAYS** handle null/undefined cases explicitly
- **NEVER** use `any` type unless absolutely necessary

### Rule 10: Testing Considerations
- **ALWAYS** write code that is easily testable
- **ALWAYS** separate business logic from UI components
- **ALWAYS** use proper dependency injection patterns
- **NEVER** write tightly coupled code that's difficult to test

## Implementation Checklist

Before marking any feature as complete, ensure:
- [ ] All async functions properly use await
- [ ] No placeholder code or TODOs remain
- [ ] All form handlers are fully implemented
- [ ] API calls include proper error handling
- [ ] Loading states are managed correctly
- [ ] User feedback is provided for all actions
- [ ] TypeScript types are properly defined
- [ ] Edge cases are handled
- [ ] Code is properly formatted and linted

## Enforcement
- **ALWAYS** run linting tools before committing code
- **ALWAYS** test all functionality before marking as complete
- **NEVER** commit code with known issues or incomplete implementations
- **NEVER** skip error handling or user feedback implementations