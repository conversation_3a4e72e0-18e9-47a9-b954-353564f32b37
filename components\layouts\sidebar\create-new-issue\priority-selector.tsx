'use client';

import { CheckIcon } from 'lucide-react';
import { useId, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { usePriorities } from '@/hooks/use-db';
import { getPriorityIcon, PriorityIcon } from '@/lib/constants/priorities';

interface PrioritySelectorProps {
  value: string;
  onChange: (priorityId: string) => void;
}

export function PrioritySelector({ value, onChange }: PrioritySelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const { priorities: priorityOptions, loading } = usePriorities();

  const handlePriorityChange = (priorityId: string) => {
    setOpen(false);
    onChange(priorityId);
  };

  return (
    <div className='*:not-first:mt-2'>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='flex items-center justify-center gap-2'
            size='sm'
            variant='secondary'
            // role='combobox'
            aria-expanded={open}
            disabled={loading}
          >
            {(() => {
              const selectedItem = priorityOptions.find(
                (item) => item.id === value
              );
              if (selectedItem) {
                return <PriorityIcon PriorityName={selectedItem.id} />;
              }
              return null;
            })()}
            <span>
              {loading
                ? 'Loading...'
                : value
                  ? priorityOptions.find((s) => s.id === value)?.name
                  : 'Select status'}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
          align='start'
        >
          <Command>
            <CommandInput placeholder='Set priority...' />
            <CommandList>
              <CommandEmpty>No priority found.</CommandEmpty>
              <CommandGroup>
                {priorityOptions.map((item) => (
                  <CommandItem
                    key={item.id}
                    value={item.id}
                    onSelect={() => handlePriorityChange(item.id)}
                    className='flex items-center justify-between'
                  >
                    <div className='flex items-center gap-2'>
                      <span className='text-muted-foreground text-sm'>
                        {(() => {
                          const IconComponent = getPriorityIcon(
                            item.name.toLowerCase()
                          );
                          return IconComponent ? (
                            <IconComponent className='size-4' />
                          ) : null;
                        })()}
                      </span>
                      {item.name}
                    </div>
                    {value === item.id && (
                      <CheckIcon size={16} className='ml-auto' />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
