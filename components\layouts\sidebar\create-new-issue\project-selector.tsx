'use client';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, FolderIcon } from 'lucide-react';
import { useId, useState } from 'react';
import { useProjects } from '@/hooks/use-db';

interface ProjectSelectorProps {
  value: string;
  onChange: (projectId: string) => void;
}

export function ProjectSelector({ value, onChange }: ProjectSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const { projects: projectOptions, loading } = useProjects();

  const handleProjectChange = (projectId: string) => {
    setOpen(false);
    onChange(projectId);
  };

  return (
    <div className='*:not-first:mt-2'>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='flex items-center justify-center gap-2'
            size='sm'
            variant='secondary'
            // role='combobox'
            aria-expanded={open}
            disabled={loading}
          >
            <FolderIcon className='size-4' />
            <span>
              {loading
                ? 'Loading...'
                : value
                  ? projectOptions.find((p) => p.id === value)?.name
                  : 'No project'}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
          align='start'
        >
          <Command>
            <CommandInput placeholder='Set project...' />
            <CommandList>
              <CommandEmpty>No projects found.</CommandEmpty>
              <CommandGroup>
                <CommandItem
                  value='no-project'
                  onSelect={() => handleProjectChange('')}
                  className='flex items-center justify-between'
                >
                  <div className='flex items-center gap-2'>
                    <FolderIcon className='size-4' />
                    No Project
                  </div>
                  {!value && <CheckIcon size={16} className='ml-auto' />}
                </CommandItem>
                {projectOptions.map((project) => (
                  <CommandItem
                    key={project.id}
                    value={project.id}
                    onSelect={() => handleProjectChange(project.id)}
                    className='flex items-center justify-between'
                  >
                    <div className='flex items-center gap-2'>
                      <FolderIcon className='size-4' />
                      {project.name}
                    </div>
                    {value === project.id && (
                      <CheckIcon size={16} className='ml-auto' />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
