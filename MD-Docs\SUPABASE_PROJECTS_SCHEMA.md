# Supabase Projects Database Schema

## Overview
This document provides the complete database schema for the projects management system extracted from the Circle project. It includes all necessary tables, relationships, security policies, and sample data for a production-ready Supabase implementation.

## Database Tables

### 1. Projects Table
```sql
CREATE TABLE projects (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT, -- Icon name/identifier for UI
  percent_complete INTEGER DEFAULT 0 CHECK (percent_complete >= 0 AND percent_complete <= 100),
  start_date DATE,
  target_date DATE,
  lead_id UUID REFERENCES profiles(id),
  status_id TEXT,
  priority_id TEXT,
  health_id TEXT CHECK (health_id IN ('no-update', 'off-track', 'on-track', 'at-risk')),
  team_id TEXT, -- Reference to team that owns this project
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id)
);
```

### 2. Project Teams Junction Table
```sql
CREATE TABLE project_teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id TEXT REFERENCES projects(id) ON DELETE CASCADE,
  team_id TEXT REFERENCES teams(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(project_id, team_id)
);
```

### 3. Project Members Junction Table
```sql
CREATE TABLE project_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id TEXT REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'member' CHECK (role IN ('lead', 'member', 'viewer')),
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(project_id, user_id)
);
```

### 4. Health Status Reference Table
```sql
CREATE TABLE health_status (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  description TEXT,
  sort_order INTEGER DEFAULT 0
);
```

## Indexes for Performance

```sql
-- Projects table indexes
CREATE INDEX idx_projects_lead_id ON projects(lead_id);
CREATE INDEX idx_projects_status_id ON projects(status_id);
CREATE INDEX idx_projects_priority_id ON projects(priority_id);
CREATE INDEX idx_projects_health_id ON projects(health_id);
CREATE INDEX idx_projects_team_id ON projects(team_id);
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_start_date ON projects(start_date);
CREATE INDEX idx_projects_target_date ON projects(target_date);
CREATE INDEX idx_projects_percent_complete ON projects(percent_complete);

-- Project teams indexes
CREATE INDEX idx_project_teams_project_id ON project_teams(project_id);
CREATE INDEX idx_project_teams_team_id ON project_teams(team_id);

-- Project members indexes
CREATE INDEX idx_project_members_project_id ON project_members(project_id);
CREATE INDEX idx_project_members_user_id ON project_members(user_id);
CREATE INDEX idx_project_members_role ON project_members(role);
```

## Row Level Security (RLS) Policies

### Projects Table Policies
```sql
-- Enable RLS
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Users can view projects they are members of or lead
CREATE POLICY "Users can view accessible projects" ON projects
  FOR SELECT USING (
    auth.uid() = lead_id OR
    auth.uid() = created_by OR
    EXISTS (
      SELECT 1 FROM project_members pm 
      WHERE pm.project_id = projects.id 
      AND pm.user_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM project_teams pt
      JOIN team_members tm ON pt.team_id = tm.team_id
      WHERE pt.project_id = projects.id 
      AND tm.user_id = auth.uid()
    )
  );

-- Users can create projects
CREATE POLICY "Users can create projects" ON projects
  FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Project leads and members with appropriate role can update projects
CREATE POLICY "Project leads and authorized members can update projects" ON projects
  FOR UPDATE USING (
    auth.uid() = lead_id OR
    auth.uid() = created_by OR
    EXISTS (
      SELECT 1 FROM project_members pm 
      WHERE pm.project_id = projects.id 
      AND pm.user_id = auth.uid() 
      AND pm.role IN ('lead', 'member')
    )
  );

-- Project leads and creators can delete projects
CREATE POLICY "Project leads and creators can delete projects" ON projects
  FOR DELETE USING (
    auth.uid() = lead_id OR
    auth.uid() = created_by
  );
```

### Project Teams Policies
```sql
ALTER TABLE project_teams ENABLE ROW LEVEL SECURITY;

-- Users can view project-team associations for projects they have access to
CREATE POLICY "Users can view project teams" ON project_teams
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = project_teams.project_id 
      AND (
        auth.uid() = p.lead_id OR
        auth.uid() = p.created_by OR
        EXISTS (
          SELECT 1 FROM project_members pm 
          WHERE pm.project_id = p.id 
          AND pm.user_id = auth.uid()
        )
      )
    )
  );

-- Project leads can manage team associations
CREATE POLICY "Project leads can manage project teams" ON project_teams
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = project_teams.project_id 
      AND (auth.uid() = p.lead_id OR auth.uid() = p.created_by)
    )
  );
```

### Project Members Policies
```sql
ALTER TABLE project_members ENABLE ROW LEVEL SECURITY;

-- Users can view project members for projects they have access to
CREATE POLICY "Users can view project members" ON project_members
  FOR SELECT USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = project_members.project_id 
      AND (
        auth.uid() = p.lead_id OR
        auth.uid() = p.created_by OR
        EXISTS (
          SELECT 1 FROM project_members pm2 
          WHERE pm2.project_id = p.id 
          AND pm2.user_id = auth.uid()
        )
      )
    )
  );

-- Project leads can manage members
CREATE POLICY "Project leads can manage members" ON project_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = project_members.project_id 
      AND (auth.uid() = p.lead_id OR auth.uid() = p.created_by)
    )
  );
```

### Health Status Policies
```sql
ALTER TABLE health_status ENABLE ROW LEVEL SECURITY;

-- All authenticated users can view health status options
CREATE POLICY "Authenticated users can view health status" ON health_status
  FOR SELECT USING (auth.role() = 'authenticated');
```

## Database Functions

### Update Timestamp Function
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';
```

### Apply Triggers
```sql
-- Projects table trigger
CREATE TRIGGER update_projects_updated_at 
  BEFORE UPDATE ON projects 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();
```

### Project Statistics Function
```sql
CREATE OR REPLACE FUNCTION get_project_stats(project_id_param TEXT)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_issues', COUNT(*),
    'completed_issues', COUNT(*) FILTER (WHERE s.name = 'Done'),
    'in_progress_issues', COUNT(*) FILTER (WHERE s.name = 'In Progress'),
    'todo_issues', COUNT(*) FILTER (WHERE s.name = 'To Do'),
    'completion_percentage', 
      CASE 
        WHEN COUNT(*) = 0 THEN 0
        ELSE ROUND((COUNT(*) FILTER (WHERE s.name = 'Done')::DECIMAL / COUNT(*)) * 100, 2)
      END
  ) INTO result
  FROM issues i
  LEFT JOIN status s ON i.status_id = s.id
  WHERE i.project_id = project_id_param;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Sample Data

### Health Status Data
```sql
INSERT INTO health_status (id, name, color, description, sort_order) VALUES
('no-update', 'No Update', '#6B7280', 'The project has not been updated recently', 1),
('off-track', 'Off Track', '#EF4444', 'The project is not on track and may be delayed', 2),
('on-track', 'On Track', '#10B981', 'The project is on track and on schedule', 3),
('at-risk', 'At Risk', '#F59E0B', 'The project is at risk and may face delays', 4);
```

### Sample Projects Data
```sql
-- Note: Replace with actual user IDs from your profiles table
INSERT INTO projects (id, name, description, icon, percent_complete, start_date, target_date, lead_id, status_id, priority_id, health_id, team_id, created_by) VALUES
('proj-1', 'Core Components Library', 'Building the foundational UI components for the design system', 'Cuboid', 80, '2025-01-15', '2025-03-15', 'user-uuid-1', 'in-progress', 'high', 'on-track', 'team-1', 'user-uuid-1'),
('proj-2', 'Theming System', 'Implementing dark/light mode and custom theming capabilities', 'Blocks', 50, '2025-02-01', '2025-04-01', 'user-uuid-2', 'in-progress', 'medium', 'at-risk', 'team-1', 'user-uuid-2'),
('proj-3', 'Modal Components', 'Creating reusable modal and dialog components', 'Vault', 0, '2025-03-01', '2025-05-01', 'user-uuid-3', 'to-do', 'low', 'no-update', 'team-2', 'user-uuid-3');
```

### Sample Project Members Data
```sql
-- Add project members (replace with actual user IDs)
INSERT INTO project_members (project_id, user_id, role) VALUES
('proj-1', 'user-uuid-1', 'lead'),
('proj-1', 'user-uuid-2', 'member'),
('proj-1', 'user-uuid-3', 'member'),
('proj-2', 'user-uuid-2', 'lead'),
('proj-2', 'user-uuid-1', 'member'),
('proj-3', 'user-uuid-3', 'lead');
```

### Sample Project Teams Data
```sql
-- Associate projects with teams
INSERT INTO project_teams (project_id, team_id) VALUES
('proj-1', 'team-1'),
('proj-2', 'team-1'),
('proj-3', 'team-2');
```

## Database Views

### Projects with Details View
```sql
CREATE VIEW projects_with_details AS
SELECT 
  p.*,
  lead.name as lead_name,
  lead.avatar_url as lead_avatar,
  s.name as status_name,
  s.color as status_color,
  pr.name as priority_name,
  pr.color as priority_color,
  hs.name as health_name,
  hs.color as health_color,
  hs.description as health_description,
  (
    SELECT COUNT(*) 
    FROM project_members pm 
    WHERE pm.project_id = p.id
  ) as member_count,
  (
    SELECT COUNT(*) 
    FROM issues i 
    WHERE i.project_id = p.id
  ) as issue_count
FROM projects p
LEFT JOIN profiles lead ON p.lead_id = lead.id
LEFT JOIN status s ON p.status_id = s.id
LEFT JOIN priorities pr ON p.priority_id = pr.id
LEFT JOIN health_status hs ON p.health_id = hs.id;
```

## Setup Instructions

1. **Run the table creation scripts** in the order provided above
2. **Create the indexes** for optimal query performance
3. **Enable RLS and create policies** to secure your data
4. **Insert sample data** to test the system
5. **Create the database functions and triggers** for automated updates
6. **Set up the views** for easier data querying

## Notes

- All foreign key references assume the existence of `profiles`, `teams`, `status`, and `priorities` tables
- The `icon` field stores icon identifiers that correspond to Lucide React icons
- Health status is managed through a separate reference table for consistency
- Project completion percentage can be calculated automatically based on associated issues
- RLS policies ensure users can only access projects they're authorized to see
- The schema supports both team-based and individual project management

This schema provides a robust foundation for project management with proper security, relationships, and performance optimization.
