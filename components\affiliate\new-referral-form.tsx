'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ProposalTypeSelector } from './proposal-type-selector';
import { useProposals } from '@/hooks/use-db';
import type { ReferalProposalType } from '@/lib/supabase/database-modules';

const newReferralSchema = z.object({
  // Client Details Section
  client_name: z.string().min(2, 'Client name must be at least 2 characters'),
  client_email: z.string().email('Please enter a valid email address'),
  client_phone: z.string().optional(),
  client_description: z
    .string()
    .min(
      10,
      'Please provide at least 10 characters describing the client needs'
    ),
  
  // Proposal Details Section
  proposal_type: z.enum(
    [
      'Graphic Design',
      'Website Development',
      'App Development',
      'Brand Development',
    ],
    {
      required_error: 'Please select a proposal type',
    }
  ),
  proposal_message: z
    .string()
    .min(
      20,
      'Please provide at least 20 characters describing the proposal details'
    ),
});

type NewReferralFormData = z.infer<typeof newReferralSchema>;

export function NewReferralForm() {
  const router = useRouter();
  const { addProposal } = useProposals(true); // Use affiliate filtering
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<NewReferralFormData>({
    resolver: zodResolver(newReferralSchema),
    defaultValues: {
      client_name: '',
      client_email: '',
      client_phone: '',
      client_description: '',
      proposal_type: undefined,
      proposal_message: '',
    },
  });

  const onSubmit = (data: NewReferralFormData) => {
    setIsSubmitting(true);

    // Create the affiliate_proposal object with the new structure
    const affiliateProposal: ReferalProposalType = {
      proposal_type: data.proposal_type,
      client_name: data.client_name,
      proposal_message: data.proposal_message,
    };

    const submitPromise = addProposal({
      client_name: data.client_name,
      client_email: data.client_email,
      client_phone: data.client_phone || null,
      client_description: data.client_description,
      affiliate_proposal: affiliateProposal,
      is_recieved: true,
      is_approved: null, // Pending approval
      completed: false,
    });

    submitPromise
      .then((newProposal) => {
        form.reset();
        router.push(`/affiliate/referrals/${newProposal.id}`);
      })
      .catch((error) => {
        console.error('Error creating referral:', error);
      })
      .finally(() => {
        setIsSubmitting(false);
      });

    toast.promise(submitPromise, {
      loading: 'Creating referral...',
      success: 'Referral created successfully!',
      error: (error) =>
        `Failed to create referral: ${error?.message || 'Unknown error'}`,
    });
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>New Client Referral</CardTitle>
        <CardDescription>
          Submit a new client referral with their details and project requirements.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Section 1: Client Details */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">Client Details</h3>
                <p className="text-sm text-muted-foreground">
                  Information about the client you're referring
                </p>
              </div>
              
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="client_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter client's full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="client_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client Email *</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="client_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter client's phone number (optional)" {...field} />
                    </FormControl>
                    <FormDescription>
                      Optional - helps with follow-up communication
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="client_description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client Needs Description *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the client's project needs, requirements, timeline, and any other relevant details..."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide detailed information about what the client is looking for
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Section 2: Proposal Details */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">Proposal Details</h3>
                <p className="text-sm text-muted-foreground">
                  Details about the type of work and your proposal
                </p>
              </div>

              <FormField
                control={form.control}
                name="proposal_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proposal Type *</FormLabel>
                    <FormControl>
                      <ProposalTypeSelector
                        value={field.value || ''}
                        onChange={field.onChange}
                        placeholder="Select the type of work needed..."
                      />
                    </FormControl>
                    <FormDescription>
                      Choose the category that best matches the client's needs
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="proposal_message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proposal Message *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your proposal, approach, timeline, and any specific details about how you would handle this project..."
                        className="min-h-[150px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide detailed information about your proposed solution and approach
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Creating...' : 'Create Referral'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
