'use client';

import { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import type { Issue } from '@/lib/supabase/database-modules';

interface CollaboratorIssuesMetricsProps {
  issues: Issue[];
  loading?: boolean;
}

export function CollaboratorIssuesMetrics({
  issues,
  loading = false,
}: CollaboratorIssuesMetricsProps) {
  const metrics = useMemo(() => {
    if (loading || !issues) {
      return {
        total: 0,
        open: 0,
        inProgress: 0,
        completed: 0,
        overdue: 0,
        assignedToMe: 0,
      };
    }

    const now = new Date();
    const total = issues.length;
    const open = issues.filter((issue) => issue.status?.name === 'Open').length;
    const inProgress = issues.filter((issue) => issue.status?.name === 'In Progress').length;
    const completed = issues.filter((issue) => issue.status?.name === 'Completed').length;
    const overdue = issues.filter((issue) => {
      if (!issue.due_date) return false;
      const dueDate = new Date(issue.due_date);
      return dueDate < now && issue.status?.name !== 'Completed';
    }).length;
    
    // For collaborators, we'll count issues assigned to them
    const assignedToMe = issues.filter((issue) => issue.assignee_id).length;

    return {
      total,
      open,
      inProgress,
      completed,
      overdue,
      assignedToMe,
    };
  }, [issues, loading]);

  const metricCards = [
    {
      title: 'Total Issues',
      value: metrics.total,
      description: 'All accessible issues',
    },
    {
      title: 'Open',
      value: metrics.open,
      description: 'Issues waiting to start',
    },
    {
      title: 'In Progress',
      value: metrics.inProgress,
      description: 'Currently being worked on',
    },
    {
      title: 'Completed',
      value: metrics.completed,
      description: 'Finished issues',
    },
    {
      title: 'Overdue',
      value: metrics.overdue,
      description: 'Past due date',
    },
    {
      title: 'Assigned to Me',
      value: metrics.assignedToMe,
      description: 'Issues I\'m working on',
    },
  ];

  if (loading) {
    return (
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4'>
        {metricCards.map((_, index) => (
          <Card key={index}>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <Skeleton className='h-4 w-20' />
            </CardHeader>
            <CardContent>
              <Skeleton className='h-8 w-12 mb-1' />
              <Skeleton className='h-3 w-24' />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4'>
      {metricCards.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>{metric.title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{metric.value}</div>
            <p className='text-xs text-muted-foreground'>{metric.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
