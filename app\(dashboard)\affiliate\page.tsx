'use client';

import { ReferralsMetrics } from '@/components/affiliate/referrals-metrics';
import { useProfile, useProposals } from '@/hooks/use-db';

export default function AffiliateDashboardPage() {
  const { profile } = useProfile();
  const { proposals, loading: proposalsLoading } = useProposals(true); // Get affiliate proposals for metrics

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 h-11 inline-flex items-center border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Affiliate Dashboard</p>
      </header>
      <section className='flex-1 p-6'>
        <div className='mx-auto space-y-6'>
          <div>
            <h1 className='text-2xl font-bold'>
              Welcome, {profile?.full_name || profile?.name || 'Affiliate'}
            </h1>
            <p className='text-muted-foreground'>
              Manage your referrals and portfolio from your dashboard.
            </p>
          </div>

          {/* Performance Metrics */}
          <div className='space-y-4'>
            <ReferralsMetrics
              proposals={proposals}
              loading={proposalsLoading}
            />
          </div>
        </div>
      </section>
    </main>
  );
}
