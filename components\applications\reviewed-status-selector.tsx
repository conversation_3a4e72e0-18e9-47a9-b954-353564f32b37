'use client';

import { Check, Clock, X } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Application } from '@/lib/supabase/database-modules';
import { cn } from '@/lib/utils';

interface ReviewedStatusSelectorProps {
  application: Application;
  onReviewedChange: (
    applicationId: number,
    reviewed: 'reviewed' | 'received' | 'notAccepted'
  ) => void;
}

const ReviewedStatusIcon = ({
  status,
}: {
  status: 'reviewed' | 'received' | 'notAccepted';
}) => {
  if (status === 'reviewed') {
    return <Check className='h-4 w-4 text-green-600' />;
  }
  if (status === 'notAccepted') {
    return <X className='h-4 w-4 text-red-600' />;
  }
  return <Clock className='h-4 w-4 text-blue-600' />;
};

const getStatusText = (
  status: 'reviewed' | 'received' | 'notAccepted'
): string => {
  if (status === 'reviewed') return 'Reviewed';
  if (status === 'notAccepted') return 'Not Accepted';
  return 'Received';
};

const getStatusColor = (
  status: 'reviewed' | 'received' | 'notAccepted'
): string => {
  if (status === 'reviewed') return 'text-green-600';
  if (status === 'notAccepted') return 'text-red-600';
  return 'text-blue-600';
};

export function ReviewedStatusSelector({
  application,
  onReviewedChange,
}: ReviewedStatusSelectorProps) {
  const [isUpdating, setIsUpdating] = useState(false);

  // Check if the reviewed status can be changed (only received applications can be changed)
  const canChangeStatus = application.reviewed === 'received';

  const handleStatusChange = (
    newStatus: 'reviewed' | 'received' | 'notAccepted'
  ) => {
    if (isUpdating || !canChangeStatus) return;

    setIsUpdating(true);
    onReviewedChange(application.id, newStatus);

    // Reset updating state after a short delay
    setTimeout(() => {
      setIsUpdating(false);
    }, 1000);
  };

  // If status cannot be changed, render as a static display
  if (!canChangeStatus) {
    return (
      <div
        className={cn(
          'h-8 px-2 flex items-center gap-2 rounded-md',
          getStatusColor(application.reviewed)
        )}
      >
        <ReviewedStatusIcon status={application.reviewed} />
        <span className='text-sm font-medium'>
          {getStatusText(application.reviewed)}
        </span>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className={cn(
            'h-8 px-2 flex items-center gap-2',
            getStatusColor(application.reviewed),
            isUpdating && 'opacity-50 cursor-not-allowed'
          )}
          disabled={isUpdating}
        >
          <ReviewedStatusIcon status={application.reviewed} />
          <span className='text-sm font-medium'>
            {getStatusText(application.reviewed)}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start'>
        <DropdownMenuItem
          onClick={() => handleStatusChange('received')}
          className='flex items-center gap-2'
        >
          <Clock className='h-4 w-4 text-blue-600' />
          <span>Received</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleStatusChange('reviewed')}
          className='flex items-center gap-2'
        >
          <Check className='h-4 w-4 text-green-600' />
          <span>Reviewed</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleStatusChange('notAccepted')}
          className='flex items-center gap-2'
        >
          <X className='h-4 w-4 text-red-600' />
          <span>Not Accepted</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
