'use client';

import { format } from 'date-fns';
import {
  Mail,
  Calendar,
  Shield,
  FileText,
  Award,
  Briefcase,
  Phone,
  MapPin,
  Clock,
  Download,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { usePortfolio, useProposals } from '@/hooks/use-db';

export function PortfolioView() {
  const { portfolioData, loading, error } = usePortfolio();
  const { proposals, loading: proposalsLoading } = useProposals(true); // Get affiliate proposals for metrics

  if (loading || proposalsLoading) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardHeader>
            <div className='flex items-center space-x-4'>
              <div className='w-16 h-16 bg-neutral-200 dark:bg-neutral-800 rounded-full animate-pulse' />
              <div className='space-y-2'>
                <div className='h-6 w-48 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse' />
                <div className='h-4 w-32 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse' />
              </div>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className='h-4 w-full bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse'
              />
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !portfolioData) {
    return (
      <div className='text-center py-12'>
        <AlertCircle className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
        <p className='text-muted-foreground'>
          {error || 'Unable to load portfolio information.'}
        </p>
      </div>
    );
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Helper function to safely format dates
  const formatDate = (
    dateString: string | null | undefined,
    formatStr: string
  ) => {
    if (!dateString) return null;

    const date = new Date(dateString);
    if (Number.isNaN(date.getTime())) return null;

    return format(date, formatStr);
  };

  const displayName =
    portfolioData.full_name || portfolioData.name || 'Affiliate User';

  // Calculate metrics from proposals
  const totalReferrals = proposals?.length || 0;
  const approvedReferrals =
    proposals?.filter((p) => p.is_approved === true).length || 0;
  const successRate =
    totalReferrals > 0
      ? Math.round((approvedReferrals / totalReferrals) * 100)
      : 0;

  // Get approval status info
  const getApprovalStatusInfo = (approved: string) => {
    switch (approved) {
      case 'accepted':
        return {
          icon: CheckCircle,
          label: 'Approved',
          variant: 'default' as const,
          className: 'text-green-600',
        };
      case 'reviewing':
        return {
          icon: Clock,
          label: 'Under Review',
          variant: 'secondary' as const,
          className: 'text-yellow-600',
        };
      case 'notAccepted':
        return {
          icon: XCircle,
          label: 'Not Approved',
          variant: 'destructive' as const,
          className: 'text-red-600',
        };
      default:
        return {
          icon: Clock,
          label: 'Pending Review',
          variant: 'outline' as const,
          className: 'text-gray-600',
        };
    }
  };

  const approvalStatus = getApprovalStatusInfo(portfolioData.approved);

  return (
    <div className='space-y-6'>
      {/* Profile Overview */}
      <Card>
        <CardHeader>
          <div className='flex items-start space-x-4'>
            <Avatar className='w-16 h-16'>
              <AvatarImage
                src={portfolioData.avatar_url || undefined}
                alt={displayName}
              />
              <AvatarFallback className='text-lg'>
                {getInitials(displayName)}
              </AvatarFallback>
            </Avatar>
            <div className='flex-1'>
              <div className='flex items-center space-x-3 flex-wrap'>
                <CardTitle className='text-2xl'>{displayName}</CardTitle>
                <Badge
                  variant='secondary'
                  className='flex items-center space-x-1'
                >
                  <Shield className='h-3 w-3' />
                  <span>{portfolioData.role}</span>
                </Badge>
                <Badge
                  variant={approvalStatus.variant}
                  className={`flex items-center space-x-1 ${approvalStatus.className}`}
                >
                  <approvalStatus.icon className='h-3 w-3' />
                  <span>{approvalStatus.label}</span>
                </Badge>
              </div>
              <CardDescription className='text-base mt-1'>
                {portfolioData.email}
              </CardDescription>
              {portfolioData.position && (
                <p className='text-sm text-muted-foreground mt-1'>
                  {portfolioData.position}
                </p>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className='grid gap-6 md:grid-cols-2'>
            <div className='space-y-3'>
              <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                Contact Information
              </h4>
              <div className='space-y-2'>
                <div className='flex items-center space-x-2 text-sm'>
                  <Mail className='h-4 w-4 text-muted-foreground' />
                  <span className='text-muted-foreground'>Email:</span>
                  <span>{portfolioData.email}</span>
                </div>
                {portfolioData.phone && (
                  <div className='flex items-center space-x-2 text-sm'>
                    <Phone className='h-4 w-4 text-muted-foreground' />
                    <span className='text-muted-foreground'>Phone:</span>
                    <span>{portfolioData.phone}</span>
                  </div>
                )}
                {portfolioData.location && (
                  <div className='flex items-center space-x-2 text-sm'>
                    <MapPin className='h-4 w-4 text-muted-foreground' />
                    <span className='text-muted-foreground'>Location:</span>
                    <span>{portfolioData.location}</span>
                  </div>
                )}
              </div>
            </div>
            <div className='space-y-3'>
              <h4 className='font-medium text-sm text-muted-foreground uppercase tracking-wide'>
                Account Details
              </h4>
              <div className='space-y-2'>
                {portfolioData.joined_date &&
                  formatDate(portfolioData.joined_date, 'MMMM dd, yyyy') && (
                    <div className='flex items-center space-x-2 text-sm'>
                      <Calendar className='h-4 w-4 text-muted-foreground' />
                      <span className='text-muted-foreground'>Joined:</span>
                      <span>
                        {formatDate(portfolioData.joined_date, 'MMMM dd, yyyy')}
                      </span>
                    </div>
                  )}
                {portfolioData.status && (
                  <div className='flex items-center space-x-2 text-sm'>
                    <div
                      className={`w-2 h-2 rounded-full ${
                        portfolioData.status === 'active'
                          ? 'bg-green-500'
                          : portfolioData.status === 'inactive'
                            ? 'bg-red-500'
                            : 'bg-gray-500'
                      }`}
                    />
                    <span className='text-muted-foreground'>Status:</span>
                    <span className='capitalize'>{portfolioData.status}</span>
                  </div>
                )}
                <div className='flex items-center space-x-2 text-sm'>
                  <Calendar className='h-4 w-4 text-muted-foreground' />
                  <span className='text-muted-foreground'>Last Updated:</span>
                  <span>
                    {formatDate(
                      portfolioData.updated_at || portfolioData.created_at,
                      'MMM dd, yyyy'
                    ) || 'Unknown'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Professional Information */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Briefcase className='h-5 w-5' />
            <span>Professional Information</span>
          </CardTitle>
          <CardDescription>
            Your professional background and expertise
          </CardDescription>
        </CardHeader>
        <CardContent>
          {portfolioData.skills ||
          portfolioData.past_experience ||
          portfolioData.interests ? (
            <div className='grid gap-6 md:grid-cols-2'>
              <div className='space-y-4'>
                {portfolioData.skills && (
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Skills</h4>
                    <p className='text-sm text-muted-foreground'>
                      {portfolioData.skills}
                    </p>
                  </div>
                )}
                {portfolioData.interests && (
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Interests</h4>
                    <p className='text-sm text-muted-foreground'>
                      {portfolioData.interests}
                    </p>
                  </div>
                )}
                {portfolioData.areas && portfolioData.areas.length > 0 && (
                  <div>
                    <h4 className='font-medium text-sm mb-2'>
                      Areas of Expertise
                    </h4>
                    <div className='flex flex-wrap gap-2'>
                      {portfolioData.areas.map((area) => (
                        <Badge key={area} variant='outline'>
                          {area}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <div className='space-y-4'>
                {portfolioData.past_experience && (
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Experience</h4>
                    <p className='text-sm text-muted-foreground whitespace-pre-wrap'>
                      {portfolioData.past_experience}
                    </p>
                  </div>
                )}
                {portfolioData.why_join && (
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Why I Joined</h4>
                    <p className='text-sm text-muted-foreground whitespace-pre-wrap'>
                      {portfolioData.why_join}
                    </p>
                  </div>
                )}
                {portfolioData.resume_url && (
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Resume</h4>
                    <Button variant='outline' size='sm' asChild>
                      <a
                        href={portfolioData.resume_url}
                        target='_blank'
                        rel='noopener noreferrer'
                      >
                        <Download className='h-4 w-4 mr-2' />
                        Download Resume
                      </a>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className='text-center py-8'>
              <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
              <p className='text-muted-foreground'>
                Professional information will be displayed here once you
                complete your profile.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Award className='h-5 w-5' />
            <span>Performance Overview</span>
          </CardTitle>
          <CardDescription>
            Your affiliate performance and achievements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid gap-4 md:grid-cols-3'>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-primary'>
                {totalReferrals}
              </div>
              <p className='text-sm text-muted-foreground mt-1'>
                Total Referrals
              </p>
            </div>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-green-600'>
                {approvedReferrals}
              </div>
              <p className='text-sm text-muted-foreground mt-1'>Approved</p>
            </div>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-blue-600'>
                {successRate}%
              </div>
              <p className='text-sm text-muted-foreground mt-1'>Success Rate</p>
            </div>
          </div>
          {totalReferrals === 0 && (
            <div className='text-center mt-4'>
              <p className='text-sm text-muted-foreground'>
                Start making referrals to see your performance metrics here.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* NDA Status */}
      {portfolioData.is_nda_signed && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center space-x-2'>
              <Shield className='h-5 w-5' />
              <span>NDA Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center space-x-2 text-sm'>
              <CheckCircle className='h-4 w-4 text-green-600' />
              <span className='text-muted-foreground'>NDA Signed:</span>
              <span>
                {portfolioData.nda_signed_date
                  ? formatDate(
                      portfolioData.nda_signed_date,
                      'MMMM dd, yyyy'
                    ) || 'Yes'
                  : 'Yes'}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
