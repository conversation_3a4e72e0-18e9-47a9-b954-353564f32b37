'use client';

import { use, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import { ArrowLeft, Save, X } from 'lucide-react';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import {
  useTeams,
  useProjects,
  useMembers,
  useTeamMembers,
} from '@/hooks/use-db';
import type { Team, TeamMember } from '@/lib/supabase/database-modules';
import { TeamMembersSelector } from '@/components/teams/team-members-selector';
import { TeamProjectsSelector } from '@/components/teams/team-projects-selector';

const editTeamSchema = z.object({
  name: z.string().min(1, 'Team name is required'),
  description: z.string().optional(),
  color: z.string().optional(),
  icon: z.string().optional(),
});

type EditTeamFormData = z.infer<typeof editTeamSchema>;

interface SelectedTeamMember {
  id: string;
  name: string;
  email: string;
  avatar_url: string | null;
  joined: boolean;
}

interface SelectedTeamProject {
  id: string;
  name: string;
  description: string | null;
  status: string;
}

interface EditTeamPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditTeamPage({ params }: EditTeamPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const { teams, loading, updateTeam } = useTeams();
  const { projects } = useProjects();
  const { members } = useMembers();
  const { fetchTeamMembersByTeam, addMember, removeMember } = useTeamMembers();

  const [team, setTeam] = useState<Team | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [selectedMembers, setSelectedMembers] = useState<SelectedTeamMember[]>(
    []
  );
  const [selectedProjects, setSelectedProjects] = useState<
    SelectedTeamProject[]
  >([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<EditTeamFormData>({
    resolver: zodResolver(editTeamSchema),
    defaultValues: {
      name: '',
      description: '',
      color: '#3b82f6',
      icon: '',
    },
  });

  useEffect(() => {
    if (!loading && teams.length > 0) {
      const foundTeam = teams.find((t) => t.id === resolvedParams.id);
      if (foundTeam) {
        setTeam(foundTeam);

        // Update form with team data
        form.reset({
          name: foundTeam.name,
          description: foundTeam.description || '',
          color: foundTeam.color || '#3b82f6',
          icon: foundTeam.icon || '',
        });

        // Fetch team members
        fetchTeamMembersByTeam(foundTeam.id)
          .then((teamMembersData) => {
            setTeamMembers(teamMembersData);

            // Convert team members to selected members format
            const selectedMembersData = teamMembersData
              .map((teamMember) => {
                const memberDetails = members.find(
                  (m) => m.id === teamMember.user_id
                );
                return memberDetails
                  ? {
                      id: memberDetails.id,
                      name:
                        memberDetails.full_name ||
                        memberDetails.name ||
                        memberDetails.email,
                      email: memberDetails.email,
                      avatar_url: memberDetails.avatar_url,
                      joined: teamMember.joined || false,
                    }
                  : null;
              })
              .filter(
                (member): member is SelectedTeamMember => member !== null
              );

            setSelectedMembers(selectedMembersData);
          })
          .catch((error) => {
            console.error('Error fetching team members:', error);
            setTeamMembers([]);
          });

        // Set selected projects (if team has projects array)
        if (foundTeam.projects && foundTeam.projects.length > 0) {
          const teamProjects = foundTeam.projects
            .map((projectId) => {
              const project = projects.find((p) => p.id === projectId);
              return project
                ? {
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    status:
                      typeof project.status === 'string'
                        ? project.status
                        : project.status?.name || 'unknown',
                  }
                : null;
            })
            .filter(
              (project): project is SelectedTeamProject => project !== null
            );

          setSelectedProjects(teamProjects);
        }
      }
    }
  }, [
    teams,
    loading,
    resolvedParams.id,
    fetchTeamMembersByTeam,
    form,
    projects,
    members,
  ]);

  const onSubmit = (data: EditTeamFormData) => {
    if (!team) return;

    setIsSubmitting(true);
    const loadingToast = toast.loading('Updating team...');

    const teamInput = {
      ...data,
      projects: selectedProjects.map((p) => p.id),
      members: selectedMembers.map((m) => m.id),
    };

    updateTeam(team.id, teamInput)
      .then((updatedTeam) => {
        // Handle member changes
        const currentMemberIds = teamMembers
          .map((tm) => tm.user_id)
          .filter((id): id is string => id !== null);
        const newMemberIds = selectedMembers.map((sm) => sm.id);

        // Members to add
        const membersToAdd = selectedMembers.filter(
          (sm) => !currentMemberIds.includes(sm.id)
        );

        // Members to remove
        const membersToRemove = teamMembers.filter(
          (tm) => tm.user_id && !newMemberIds.includes(tm.user_id)
        );

        const memberPromises = [
          ...membersToAdd.map((member) =>
            addMember({
              team_id: team.id,
              user_id: member.id,
              joined: member.joined,
            })
          ),
          ...membersToRemove.map((member) => removeMember(member.id)),
        ];

        return Promise.all(memberPromises).then(() => updatedTeam);
      })
      .then((updatedTeam) => {
        toast.dismiss(loadingToast);
        toast.success(`Team "${updatedTeam.name}" updated successfully!`);
        router.push(`/admin/teams/${team.id}`);
      })
      .catch((error) => {
        toast.dismiss(loadingToast);
        console.error('Failed to update team:', error);
        toast.error('Failed to update team. Please try again.');
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  if (loading) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.back()}
              className='h-8 w-8 p-0'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <p className='font-medium text-sm'>Edit Team</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-muted-foreground'>Loading team details...</div>
          </div>
        </section>
      </main>
    );
  }

  if (!team) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.back()}
              className='h-8 w-8 p-0'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <p className='font-medium text-sm'>Team Not Found</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-destructive'>Team not found</div>
          </div>
        </section>
      </main>
    );
  }

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.back()}
            className='h-8 w-8 p-0'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <p className='font-medium text-sm'>Edit Team: {team.name}</p>
        </div>
        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            <X className='h-4 w-4 mr-2' />
            Cancel
          </Button>
        </div>
      </header>

      <section className='flex-1 p-6'>
        <div className='max-w-4xl mx-auto'>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <Card>
                <CardHeader>
                  <CardTitle>Team Details</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <FormField
                    control={form.control}
                    name='name'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Team Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder='Enter team name'
                            disabled={isSubmitting}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='description'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder='Enter team description'
                            disabled={isSubmitting}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className='grid grid-cols-2 gap-4'>
                    <FormField
                      control={form.control}
                      name='color'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Color</FormLabel>
                          <FormControl>
                            <Input
                              type='color'
                              disabled={isSubmitting}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='icon'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Icon (Emoji)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder='🚀'
                              disabled={isSubmitting}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Team Members</CardTitle>
                </CardHeader>
                <CardContent>
                  <TeamMembersSelector
                    selectedMembers={selectedMembers}
                    onMembersChange={setSelectedMembers}
                    disabled={isSubmitting}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Associated Projects</CardTitle>
                </CardHeader>
                <CardContent>
                  <TeamProjectsSelector
                    selectedProjects={selectedProjects}
                    onProjectsChange={setSelectedProjects}
                    disabled={isSubmitting}
                  />
                </CardContent>
              </Card>

              <div className='flex gap-3 pt-4'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                  className='flex-1'
                >
                  Cancel
                </Button>
                <Button
                  type='submit'
                  disabled={isSubmitting}
                  className='flex-1'
                >
                  <Save className='h-4 w-4 mr-2' />
                  {isSubmitting ? 'Updating...' : 'Update Team'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </section>
    </main>
  );
}
