'use client';

import { useMemo } from 'react';

import { useIssues } from '@/hooks/use-db';
import { useSearchStore } from '@/lib/store/search-store';

import { IssueLine } from './issue-line';

export function SearchIssues() {
  const { searchIssues } = useIssues();
  const { searchQuery, isSearchOpen } = useSearchStore();

  // Use useMemo to compute search results efficiently
  const searchResults = useMemo(() => {
    if (searchQuery.trim() === '') {
      return [];
    }
    return searchIssues(searchQuery);
  }, [searchQuery, searchIssues]);

  if (!isSearchOpen) {
    return null;
  }

  return (
    <div className='w-full'>
      {searchQuery.trim() !== '' && (
        <div>
          {searchResults.length > 0 ? (
            <div className='border rounded-md mt-4'>
              <div className='py-2 px-4 border-b bg-muted/50'>
                <h3 className='text-sm font-medium'>
                  Results ({searchResults.length})
                </h3>
              </div>
              <div className='divide-y'>
                {searchResults.map((issue) => (
                  <IssueLine key={issue.id} issue={issue} layoutId={false} />
                ))}
              </div>
            </div>
          ) : (
            <div className='text-center py-8 text-muted-foreground'>
              No results found for &quot;{searchQuery}&quot;
            </div>
          )}
        </div>
      )}
    </div>
  );
}
