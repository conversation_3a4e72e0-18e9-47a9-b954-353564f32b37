'use client';

import { Edit } from 'lucide-react';
import Link from 'next/link';

import { PortfolioView } from '@/components/affiliate/portfolio-view';
import { Button } from '@/components/ui/button';

export default function PortfolioPage() {
  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Portfolio</p>
        <Link href='/affiliate/portfolio/edit'>
          <Button size='sm' className='h-8'>
            <Edit className='h-4 w-4 mr-2' />
            Edit Profile
          </Button>
        </Link>
      </header>
      <section className='flex-1 p-6'>
        <div className='max-w-4xl mx-auto'>
          <PortfolioView />
        </div>
      </section>
    </main>
  );
}
