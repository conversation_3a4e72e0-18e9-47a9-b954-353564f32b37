'use client';

import { Plus, ArrowLeft, AlertCircle } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable } from '@/components/ui/data-table';
import { createColumns } from '@/components/tables/issues/columns';
import {
  useProjects,
  useProfile,
  useIssues,
} from '@/hooks/use-db';
import type { Project, Issue } from '@/lib/supabase/database-modules';

interface IssueMetrics {
  total: number;
  open: number;
  inProgress: number;
  completed: number;
  overdue: number;
}

export default function CollaboratorProjectIssuesPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const { fetchProject, checkProjectAccess } = useProjects();
  const { profile } = useProfile();
  const { getProjectIssues } = useIssues();
  
  const [project, setProject] = useState<Project | null>(null);
  const [issues, setIssues] = useState<Issue[]>([]);
  const [loading, setLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);

  // Check access and load project data
  useEffect(() => {
    if (!profile?.id || !projectId) return;

    setLoading(true);

    // First check if user has access to this project
    checkProjectAccess(projectId, profile.id)
      .then((access) => {
        if (!access) {
          toast.error('You do not have access to this project');
          router.push('/collaborator/projects');
          return;
        }
        
        setHasAccess(true);
        
        // Load project details
        return fetchProject(projectId);
      })
      .then((projectData) => {
        if (projectData) {
          setProject(projectData);
        }
        
        // Load project issues
        return getProjectIssues(projectId);
      })
      .then((projectIssues) => {
        setIssues(projectIssues);
      })
      .catch((error) => {
        console.error('Error loading project data:', error);
        toast.error('Failed to load project data');
        router.push('/collaborator/projects');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [projectId, profile?.id, checkProjectAccess, fetchProject, getProjectIssues, router]);

  // Calculate issue metrics
  const metrics: IssueMetrics = {
    total: issues.length,
    open: issues.filter(issue => 
      issue.status?.name !== 'Done' && issue.status?.name !== 'Completed'
    ).length,
    inProgress: issues.filter(issue => 
      issue.status?.name === 'In Progress' || issue.status?.name === 'In Review'
    ).length,
    completed: issues.filter(issue => 
      issue.status?.name === 'Done' || issue.status?.name === 'Completed'
    ).length,
    overdue: issues.filter(issue => {
      if (!issue.due_date || issue.status?.name === 'Done') return false;
      return new Date(issue.due_date) < new Date();
    }).length,
  };

  const handleViewIssue = (issue: Issue) => {
    // Navigate to issue detail or open modal
    console.log('View issue:', issue);
  };

  const columns = createColumns({
    onViewIssue: handleViewIssue,
  });

  if (loading) {
    return (
      <div className='flex items-center justify-center h-full'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading project issues...</p>
        </div>
      </div>
    );
  }

  if (!hasAccess || !project) {
    return (
      <div className='flex items-center justify-center h-full'>
        <div className='text-center'>
          <AlertCircle className='h-12 w-12 text-red-500 mx-auto mb-4' />
          <h2 className='text-xl font-semibold mb-2'>Access Denied</h2>
          <p className='text-muted-foreground mb-4'>
            You do not have permission to view issues for this project.
          </p>
          <Button onClick={() => router.push('/collaborator/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.push(`/collaborator/projects/${projectId}`)}
            className='h-8 w-8 p-0'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <div>
            <p className='font-medium text-sm'>Issues</p>
            <p className='text-xs text-muted-foreground'>{project.name}</p>
          </div>
        </div>
        <div className='flex items-center gap-2'>
          <Button size='sm'>
            <Plus className='h-4 w-4 mr-2' />
            New Issue
          </Button>
        </div>
      </header>

      <section className='flex-1 p-6 space-y-6'>
        {/* Issue Metrics */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium text-muted-foreground'>
                Total Issues
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{metrics.total}</div>
              <p className='text-xs text-muted-foreground'>
                All issues in project
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium text-muted-foreground'>
                Open Issues
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{metrics.open}</div>
              <p className='text-xs text-muted-foreground'>
                Awaiting work or review
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium text-muted-foreground'>
                In Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{metrics.inProgress}</div>
              <p className='text-xs text-muted-foreground'>
                Currently being worked on
              </p>
            </CardContent>
          </Card>

          <Card className={metrics.overdue > 0 ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950' : ''}>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium text-muted-foreground'>
                Overdue
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{metrics.overdue}</div>
              <p className='text-xs text-muted-foreground'>
                Past due date
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Issues Table */}
        <Card>
          <CardHeader>
            <CardTitle>Project Issues</CardTitle>
          </CardHeader>
          <CardContent>
            {issues.length > 0 ? (
              <DataTable columns={columns} data={issues} />
            ) : (
              <div className='text-center py-12'>
                <div className='text-muted-foreground mb-4'>
                  No issues found for this project
                </div>
                <Button>
                  <Plus className='h-4 w-4 mr-2' />
                  Create First Issue
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </section>
    </main>
  );
}
