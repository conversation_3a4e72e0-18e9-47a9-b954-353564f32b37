'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, UserCircle } from 'lucide-react';
import { useEffect, useId, useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { supabaseClient } from '@/lib/supabase/auth/client';

interface UserOption {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
}

interface AssigneeSelectorProps {
  value: string;
  onChange: (userId: string) => void;
}

export function AssigneeSelector({ value, onChange }: AssigneeSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [loading, setLoading] = useState(true);

  // Load user options from Supabase profiles table (collaborators only)
  useEffect(() => {
    const loadUserOptions = async () => {
      try {
        setLoading(true);

        // Fetch users with collaborator role from profiles table
        const { data, error } = await supabaseClient
          .from('profiles')
          .select('id, full_name, email, avatar_url')
          .eq('role', 'Collaborator')
          .order('full_name');

        if (error) {
          console.error('Failed to load user options:', error);
        } else {
          const users =
            data?.map((profile) => ({
              id: profile.id,
              name: profile.full_name || profile.email,
              email: profile.email,
              avatar_url: profile.avatar_url,
            })) || [];
          setUserOptions(users);
        }
      } catch (error) {
        console.error('Failed to load user options:', error);
      } finally {
        setLoading(false);
      }
    };

    loadUserOptions();
  }, []);

  const handleAssigneeChange = (userId: string) => {
    setOpen(false);
    onChange(userId);
  };

  return (
    <div className='*:not-first:mt-2'>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='flex items-center justify-center gap-2'
            size='sm'
            variant='secondary'
            // role='combobox'
            aria-expanded={open}
            disabled={loading}
          >
            {value ? (
              (() => {
                const selectedUser = userOptions.find(
                  (user) => user.id === value
                );
                if (selectedUser) {
                  return (
                    <Avatar className='size-5'>
                      <AvatarImage
                        src={selectedUser.avatar_url || undefined}
                        alt={selectedUser.name}
                      />
                      <AvatarFallback>
                        {selectedUser.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                  );
                }
                return <UserCircle className='size-5' />;
              })()
            ) : (
              <UserCircle className='size-5' />
            )}
            <span>
              {loading
                ? 'Loading...'
                : value
                  ? userOptions.find((user) => user.id === value)?.name
                  : 'Unassigned'}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
          align='start'
        >
          <Command>
            <CommandInput placeholder='Assign to...' />
            <CommandList>
              <CommandEmpty>No users found.</CommandEmpty>
              <CommandGroup>
                <CommandItem
                  value='unassigned'
                  onSelect={() => handleAssigneeChange('')}
                  className='flex items-center justify-between'
                >
                  <div className='flex items-center gap-2'>
                    <UserCircle className='size-5' />
                    Unassigned
                  </div>
                  {!value && <CheckIcon size={16} className='ml-auto' />}
                </CommandItem>
                {userOptions.map((user) => (
                  <CommandItem
                    key={user.id}
                    value={user.id}
                    onSelect={() => handleAssigneeChange(user.id)}
                    className='flex items-center justify-between'
                  >
                    <div className='flex items-center gap-2'>
                      <Avatar className='size-5'>
                        <AvatarImage
                          src={user.avatar_url || undefined}
                          alt={user.name}
                        />
                        <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      {user.name}
                    </div>
                    {value === user.id && (
                      <CheckIcon size={16} className='ml-auto' />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
