'use client';

import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

import { EditReferralForm } from '@/components/affiliate/edit-referral-form';
import { But<PERSON> } from '@/components/ui/button';

export default function EditReferralPage() {
  const params = useParams();
  const referralId = parseInt(params.id as string, 10);

  if (Number.isNaN(referralId)) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center space-x-4'>
            <Link href='/affiliate/referrals'>
              <Button variant='ghost' size='sm' className='h-8'>
                <ArrowLeft className='h-4 w-4 mr-2' />
                Back
              </Button>
            </Link>
            <p className='font-medium text-sm'>Edit Referral</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='text-center py-12'>
            <p className='text-destructive font-medium'>Invalid referral ID</p>
            <p className='text-sm text-muted-foreground mt-2'>
              Please check the URL and try again.
            </p>
          </div>
        </section>
      </main>
    );
  }

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center space-x-4'>
          <Link href={`/affiliate/referrals/${referralId}`}>
            <Button variant='ghost' size='sm' className='h-8'>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back
            </Button>
          </Link>
          <p className='font-medium text-sm'>Edit Referral</p>
        </div>
      </header>
      <section className='flex-1 p-6'>
        <EditReferralForm referralId={referralId} />
      </section>
    </main>
  );
}
