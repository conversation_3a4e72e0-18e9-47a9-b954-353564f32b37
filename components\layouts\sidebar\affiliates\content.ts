import {
  Cog,
  DollarSign,
  FileText,
  Handshake,
  Home,
  Inbox,
  Shield,
  User,
  Zap,
} from 'lucide-react';
import type { SidebarData } from '@/lib/supabase/database-modules';
import type { Database } from '@/lib/supabase/database-types';

type Profile_Types = Database['public']['Tables']['profiles']['Row'];

const affiliateSidebarData = ({
  profile,
}: {
  profile: Profile_Types;
}): SidebarData => {
  const user = {
    name: profile.full_name,
    email: profile.email,
    avatar: '/avatars/affiliate.jpg',
    role: profile.role,
  };
  const teams = [
    {
      name: 'Admin Panel',
      logo: Shield,
      plan: 'Enterprise',
    },
  ];
  const navHeader = [
    {
      title: 'Home',
      url: '/affiliate',
      icon: Home,
    },
    {
      title: 'Inbox',
      url: '/affiliate/inbox',
      icon: Inbox,
    },
  ];
  const navMain = [
    {
      title: 'workspace',
      items: [
        {
          title: 'Referrals',
          url: '/affiliate/referrals',
          icon: Handshake,
          isActive: true,
        },
      ],
    },
    {
      title: 'Finance',
      items: [
        {
          title: 'Revenue',
          url: '/affiliate/revenue',
          icon: Zap,
          isActive: true,
        },
        {
          title: 'Payouts',
          url: '/affiliate/payouts',
          icon: DollarSign,
          isActive: true,
        },
        {
          title: 'Invoces',
          url: '/affiliate/invoices',
          icon: FileText,
          isActive: true,
        },
      ],
    },
    {
      title: 'Profile',
      items: [
        {
          title: 'Portfolio',
          url: '/affiliate/portfolio',
          icon: User,
          isActive: true,
        },
        {
          title: 'Settings',
          url: '/affiliate/settings',
          icon: Cog,
          isActive: true,
        },
      ],
    },
    {
      title: 'Legal',
      items: [
        {
          title: 'NDA Agreement',
          url: '/affiliate/nda',
          icon: Shield,
          isActive: true,
        },
      ],
    },
  ];

  return {
    user,
    teams,
    navMain,
    navHeader,
  };
};

export default affiliateSidebarData;
