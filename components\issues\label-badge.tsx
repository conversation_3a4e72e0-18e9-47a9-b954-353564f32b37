import { Badge } from '@/components/ui/badge';
import { getHexColor } from '@/lib/utils/color-utils';

interface LabelOption {
  id: string;
  name: string;
  color: string;
}

interface LabelBadgeProps {
  labels: LabelOption[];
}

export function LabelBadge({ labels }: LabelBadgeProps) {
  return (
    <>
      {labels.map((label) => (
        <Badge
          key={label.id}
          variant='outline'
          className='gap-1.5 rounded-full text-muted-foreground bg-background'
        >
          <span
            className='size-1.5 rounded-full'
            style={{ backgroundColor: getHexColor(label.color) }}
            aria-hidden='true'
          ></span>
          {label.name}
        </Badge>
      ))}
    </>
  );
}
