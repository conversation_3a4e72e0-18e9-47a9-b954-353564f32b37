'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Eye, Folder, Plus, FileText, CheckSquare } from 'lucide-react';
import Link from 'next/link';

import { toast } from 'sonner';

import { DatePicker } from '@/components/projects/date-picker';
import { HealthPopover } from '@/components/projects/health-popover';
import { StatusWithPercent } from '@/components/projects/status-with-percent';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useProjects } from '@/hooks/use-db';
import type { Project } from '@/lib/supabase/database-modules';
import { PriorityIcon } from '@/lib/constants/priorities';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CreateIssueDialog } from '@/components/projects/create-issue-dialog';
import { CreateTaskDialog } from '@/components/projects/create-task-dialog';

export const createCollaboratorProjectsColumns = (): ColumnDef<Project>[] => {
  return [
    {
      accessorKey: 'name',
      header: 'Project',
      cell: ({ row }) => {
        const project = row.original;
        return (
          <div className='flex items-center gap-2'>
            <div className='relative'>
              <div className='inline-flex size-6 bg-muted/50 items-center justify-center rounded shrink-0'>
                <Folder className='size-4' />
              </div>
            </div>
            <div className='flex flex-col items-start overflow-hidden'>
              <Link
                href={`/collaborator/projects/${project.id}`}
                className='font-medium truncate w-full hover:underline'
              >
                {project.name}
              </Link>
              {project.description && (
                <span className='text-xs text-muted-foreground truncate w-full'>
                  {project.description}
                </span>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const project = row.original;
        const { updateProject } = useProjects();

        const handleStatusChange = (statusId: string) => {
          updateProject(project.id, {
            status_id: statusId,
          })
            .then(() => {
              toast.success('Project status updated');
            })
            .catch((error) => {
              toast.error('Failed to update project status');
              console.error('Error updating project status:', error);
            });
        };

        // Transform project status to match StatusOption interface
        const statusOption = project.status
          ? {
              id: project.status.id,
              name: project.status.name,
              color: project.status.color,
              sort_order: 0, // Default sort order since it's not available in project status
            }
          : null;

        return (
          <StatusWithPercent
            status={statusOption}
            percentComplete={project.percent_complete || 0}
            onStatusChange={handleStatusChange}
          />
        );
      },
    },
    {
      accessorKey: 'priority',
      header: 'Priority',
      cell: ({ row }) => {
        const project = row.original;
        const priority = project.priority;

        if (!priority) {
          return <span className='text-muted-foreground'>No priority set</span>;
        }

        return (
          <div className='flex items-center gap-2'>
            {priority.icon_name && (
              <PriorityIcon PriorityName={priority.icon_name} />
            )}
            <span className='text-sm font-medium'>{priority.name}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'health_id',
      header: 'Health',
      cell: ({ row }) => {
        const project = row.original;
        const { updateProject } = useProjects();

        const handleHealthChange = (healthId: string) => {
          updateProject(project.id, {
            health_id: healthId as
              | 'no-update'
              | 'off-track'
              | 'on-track'
              | 'at-risk',
          })
            .then(() => {
              toast.success('Project health updated');
            })
            .catch((error) => {
              toast.error('Failed to update project health');
              console.error('Error updating project health:', error);
            });
        };

        return (
          <HealthPopover
            project={{
              id: project.id,
              name: project.name,
              description: project.description,
              health: project.health || {
                id: project.health_id || 'no-update',
                name:
                  project.health_id === 'on-track'
                    ? 'On Track'
                    : project.health_id === 'off-track'
                      ? 'Off Track'
                      : project.health_id === 'at-risk'
                        ? 'At Risk'
                        : 'No Update',
                description:
                  project.health_id === 'on-track'
                    ? 'Project is progressing as planned'
                    : project.health_id === 'off-track'
                      ? 'Project is behind schedule or facing issues'
                      : project.health_id === 'at-risk'
                        ? 'Project may face delays or issues'
                        : 'No recent status update available',
              },
              lead: project.lead
                ? {
                    id: project.lead.id,
                    name: project.lead.full_name || project.lead.email,
                    avatar_url: project.lead.avatar_url,
                  }
                : undefined,
            }}
            onHealthChange={handleHealthChange}
          />
        );
      },
    },
    {
      accessorKey: 'target_date',
      header: 'Due Date',
      cell: ({ row }) => {
        const project = row.original;
        const { updateProject } = useProjects();

        const handleDateChange = (date: Date | undefined) => {
          const dateString = date ? date.toISOString() : null;
          updateProject(project.id, { target_date: dateString })
            .then(() => {
              toast.success('Project due date updated');
            })
            .catch((error) => {
              toast.error('Failed to update project due date');
              console.error('Error updating project due date:', error);
            });
        };

        return (
          <DatePicker
            date={
              project.target_date ? new Date(project.target_date) : undefined
            }
            onDateChange={handleDateChange}
          />
        );
      },
    },
    {
      accessorKey: 'lead',
      header: 'Lead',
      cell: ({ row }) => {
        const project = row.original;
        const lead = project.lead;

        if (!lead) {
          return (
            <span className='text-muted-foreground'>No lead assigned</span>
          );
        }

        return (
          <div className='flex items-center gap-2'>
            <Avatar className='size-6 mr-1'>
              <AvatarImage
                src={lead.avatar_url || undefined}
                alt={lead.full_name || lead.email}
              />
              <AvatarFallback>{lead.email[0] || 'UN'.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className='text-sm'>{lead.full_name || lead.email}</span>
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const project = row.original;

        return (
          <div className='flex items-center gap-1'>
            <Button variant='ghost' size='sm' asChild>
              <Link href={`/collaborator/projects/${project.id}`}>
                <Eye className='size-4' />
              </Link>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' size='sm'>
                  <Plus className='size-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem asChild>
                  <CreateIssueDialog
                    projectId={project.id}
                    projectName={project.name}
                    trigger={
                      <div className='flex items-center w-full cursor-pointer'>
                        <FileText className='h-4 w-4 mr-2' />
                        Create Issue
                      </div>
                    }
                  />
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <CreateTaskDialog
                    projectId={project.id}
                    projectName={project.name}
                    trigger={
                      <div className='flex items-center w-full cursor-pointer'>
                        <CheckSquare className='h-4 w-4 mr-2' />
                        Create Task
                      </div>
                    }
                  />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];
};
