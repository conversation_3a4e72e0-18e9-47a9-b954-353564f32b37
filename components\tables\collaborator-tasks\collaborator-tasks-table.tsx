'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { DataTable } from '@/components/ui/data-table';
import { useTasks, usePortfolio } from '@/hooks/use-db';
import type { Task } from '@/lib/supabase/database-modules';
import { columns } from './columns';

interface CollaboratorTasksTableProps {
  tasks?: Task[];
  loading?: boolean;
}

export function CollaboratorTasksTable({
  tasks: externalTasks,
  loading: externalLoading,
}: CollaboratorTasksTableProps = {}) {
  const { getCollaboratorTasks } = useTasks();
  const { portfolioData } = usePortfolio();
  const [internalTasks, setInternalTasks] = useState<Task[]>([]);
  const [internalLoading, setInternalLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use external data if provided, otherwise fetch internally
  const tasks = externalTasks !== undefined ? externalTasks : internalTasks;
  const loading = externalLoading !== undefined ? externalLoading : internalLoading;

  useEffect(() => {
    // Don't fetch if external data is provided or if no user
    if (externalTasks !== undefined || !portfolioData?.id) return;

    setInternalLoading(true);
    setError(null);

    getCollaboratorTasks(portfolioData.id)
      .then((collaboratorTasks) => {
        setInternalTasks(collaboratorTasks);
      })
      .catch((error) => {
        console.error('Error fetching collaborator tasks:', error);
        setError('Failed to load tasks');
        toast.error('Failed to load tasks');
      })
      .finally(() => {
        setInternalLoading(false);
      });
  }, [portfolioData?.id, getCollaboratorTasks, externalTasks]);

  if (error) {
    return (
      <div className='flex items-center justify-center h-32'>
        <p className='text-muted-foreground'>{error}</p>
      </div>
    );
  }

  return (
    <DataTable
      columns={columns}
      data={tasks}
      loading={loading}
      searchKey='title'
      searchPlaceholder='Search tasks...'
    />
  );
}
