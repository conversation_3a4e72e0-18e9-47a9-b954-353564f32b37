'use client';

import { useState } from 'react';
import { Check, Plus, X, Users } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { useMembers } from '@/hooks/use-db';
import type { Member } from '@/lib/supabase/database-modules';

interface SelectedTeamMember {
  id: string;
  name: string;
  email: string;
  avatar_url: string | null;
  joined: boolean;
}

interface TeamMembersSelectorProps {
  selectedMembers: SelectedTeamMember[];
  onMembersChange: (members: SelectedTeamMember[]) => void;
  disabled?: boolean;
}

export function TeamMembersSelector({
  selectedMembers,
  onMembersChange,
  disabled = false,
}: TeamMembersSelectorProps) {
  const { members } = useMembers();
  const [open, setOpen] = useState(false);

  // Filter out affiliates and already selected members
  const availableMembers = members.filter(
    (member) =>
      !selectedMembers.some((selected) => selected.id === member.id) &&
      member.role !== 'Affiliate' // Exclude affiliates from team membership
  );

  const addMember = (member: Member, joined: boolean = true) => {
    // Check if the user is an affiliate
    if (member.role === 'Affiliate') {
      console.warn('Attempted to add affiliate as team member:', member);
      return;
    }

    const newMember: SelectedTeamMember = {
      id: member.id,
      name: member.full_name || member.name || member.email,
      email: member.email,
      avatar_url: member.avatar_url,
      joined,
    };
    onMembersChange([...selectedMembers, newMember]);
    setOpen(false);
  };

  const removeMember = (memberId: string) => {
    onMembersChange(selectedMembers.filter((member) => member.id !== memberId));
  };

  const toggleMemberJoinedStatus = (memberId: string) => {
    onMembersChange(
      selectedMembers.map((member) =>
        member.id === memberId ? { ...member, joined: !member.joined } : member
      )
    );
  };

  return (
    <div className='space-y-4'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Users className='h-4 w-4' />
          <span className='text-sm font-medium'>Team Members</span>
          {selectedMembers.length > 0 && (
            <Badge variant='secondary' className='text-xs'>
              {selectedMembers.length}
            </Badge>
          )}
        </div>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              size='sm'
              disabled={disabled}
              className='h-8'
            >
              <Plus className='h-4 w-4 mr-2' />
              Add Member
            </Button>
          </PopoverTrigger>
          <PopoverContent className='w-80 p-0' align='end'>
            <Command>
              <CommandInput placeholder='Search members...' />
              <CommandList>
                <CommandEmpty>No members found.</CommandEmpty>
                <CommandGroup>
                  {availableMembers.map((member) => (
                    <CommandItem
                      key={member.id}
                      onSelect={() => addMember(member)}
                      className='flex items-center gap-3 p-3'
                    >
                      <Avatar className='h-8 w-8'>
                        <AvatarImage src={member.avatar_url || ''} />
                        <AvatarFallback>
                          {(member.full_name || member.name || member.email)
                            .charAt(0)
                            .toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className='flex-1 min-w-0'>
                        <p className='text-sm font-medium truncate'>
                          {member.full_name || member.name}
                        </p>
                        <p className='text-xs text-muted-foreground truncate'>
                          {member.email}
                        </p>
                      </div>
                      <Badge variant='outline' className='text-xs'>
                        {member.role}
                      </Badge>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      {selectedMembers.length === 0 ? (
        <div className='text-center py-6 text-muted-foreground'>
          <Users className='h-8 w-8 mx-auto mb-2 opacity-50' />
          <p className='text-sm'>No members selected</p>
          <p className='text-xs'>Add team members to collaborate on projects</p>
        </div>
      ) : (
        <div className='space-y-2'>
          {selectedMembers.map((member) => (
            <div
              key={member.id}
              className='flex items-center justify-between p-3 border bg-card hover:bg-accent/50 transition-colors'
            >
              <div className='flex items-center gap-3'>
                <Avatar className='h-8 w-8'>
                  <AvatarImage src={member.avatar_url || ''} />
                  <AvatarFallback>
                    {member.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className='flex-1 min-w-0'>
                  <p className='text-sm font-medium truncate'>{member.name}</p>
                  <p className='text-xs text-muted-foreground truncate'>
                    {member.email}
                  </p>
                </div>
              </div>
              <div className='flex items-center gap-2'>
                <Button
                  variant={member.joined ? 'default' : 'outline'}
                  size='sm'
                  onClick={() => toggleMemberJoinedStatus(member.id)}
                  disabled={disabled}
                  className='h-7 px-2 text-xs'
                >
                  {member.joined ? (
                    <>
                      <Check className='h-3 w-3 mr-1' />
                      Joined
                    </>
                  ) : (
                    'Invite'
                  )}
                </Button>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => removeMember(member.id)}
                  disabled={disabled}
                  className='h-7 w-7 p-0 text-muted-foreground hover:text-destructive'
                >
                  <X className='h-3 w-3' />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
